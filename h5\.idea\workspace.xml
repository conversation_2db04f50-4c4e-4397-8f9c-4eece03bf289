<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a435dab8-b985-43d3-8c5b-1c9d17c37bc9" name="更改" comment="polish">
      <change beforePath="$PROJECT_DIR$/src/views/HomeView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/HomeView.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Vue Composition API Component" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/node_modules/.pnpm/weixin-js-sdk@1.6.5/node_modules/weixin-js-sdk/index.d.ts" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30RXzMmWcnyO7Xm2IDroy6hA8HQ" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;javascript.nodejs.core.library.configured.version&quot;: &quot;24.4.1&quot;,
    &quot;javascript.nodejs.core.library.typings.version&quot;: &quot;24.2.0&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/projects/MyProjects/250726-无畏契约周年庆/h5/src/assets/images/area&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_interpreter_path&quot;: &quot;node&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;npm.build.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;D:\\projects\\MyProjects\\250726-无畏契约周年庆\\h5\\node_modules\\prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\projects\\MyProjects\\250726-无畏契约周年庆\\h5\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;vue.recent.templates&quot;: [
      &quot;Vue Composition API Component&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\projects\MyProjects\250726-无畏契约周年庆\h5\src\assets\images\area" />
      <recent name="D:\projects\MyProjects\250726-无畏契约周年庆\h5\src\views\area" />
      <recent name="D:\projects\MyProjects\250726-无畏契约周年庆\h5\src\utils" />
      <recent name="D:\projects\MyProjects\250726-无畏契约周年庆\h5\src\assets\images" />
      <recent name="D:\projects\MyProjects\250726-无畏契约周年庆\h5" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration name="build" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.build" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a435dab8-b985-43d3-8c5b-1c9d17c37bc9" name="更改" comment="" />
      <created>1753591622835</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753591622835</updated>
      <workItem from="1753591623828" duration="29392000" />
      <workItem from="1753666899492" duration="5636000" />
      <workItem from="1753867668828" duration="2176000" />
      <workItem from="1753926421347" duration="2772000" />
      <workItem from="1754271510192" duration="625000" />
      <workItem from="1754317635796" duration="18426000" />
      <workItem from="1754398211952" duration="5010000" />
    </task>
    <task id="LOCAL-00001" summary="初始化项目，接入微信SDK">
      <option name="closed" value="true" />
      <created>1753607674643</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753607674643</updated>
    </task>
    <task id="LOCAL-00002" summary="初始化项目，接入微信SDK">
      <option name="closed" value="true" />
      <created>1753610171789</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753610171789</updated>
    </task>
    <task id="LOCAL-00003" summary="feat(src): 重构首页逻辑并添加扫一扫功能&#10;&#10;- 移除了自动导入和组件声明文件，减少冗余代码&#10;- 重构了 HomeView.vue 中的登录和界面逻辑&#10;- 添加了扫一扫功能，支持微信授权登录&#10;- 优化了用户状态初始化流程&#10;-引入 vconsole 调试工具">
      <option name="closed" value="true" />
      <created>1753622268767</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753622268767</updated>
    </task>
    <task id="LOCAL-00004" summary="polish">
      <option name="closed" value="true" />
      <created>1753626125331</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753626125331</updated>
    </task>
    <task id="LOCAL-00005" summary="polish">
      <option name="closed" value="true" />
      <created>1753627107115</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753627107115</updated>
    </task>
    <task id="LOCAL-00006" summary="polish">
      <option name="closed" value="true" />
      <created>1753627717867</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753627717867</updated>
    </task>
    <task id="LOCAL-00007" summary="polish">
      <option name="closed" value="true" />
      <created>1754334779776</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1754334779776</updated>
    </task>
    <task id="LOCAL-00008" summary="polish">
      <option name="closed" value="true" />
      <created>1754334785863</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1754334785863</updated>
    </task>
    <task id="LOCAL-00009" summary="polish">
      <option name="closed" value="true" />
      <created>1754403037982</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1754403037982</updated>
    </task>
    <option name="localTasksCounter" value="10" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化项目，接入微信SDK" />
    <MESSAGE value="feat(src): 重构首页逻辑并添加扫一扫功能&#10;&#10;- 移除了自动导入和组件声明文件，减少冗余代码&#10;- 重构了 HomeView.vue 中的登录和界面逻辑&#10;- 添加了扫一扫功能，支持微信授权登录&#10;- 优化了用户状态初始化流程&#10;-引入 vconsole 调试工具" />
    <MESSAGE value="polish" />
    <option name="LAST_COMMIT_MESSAGE" value="polish" />
  </component>
</project>