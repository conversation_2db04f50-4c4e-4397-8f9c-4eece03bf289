<template>
  <div class="scrollable scrollable-image-view">
    <!-- 返回按钮 -->
    <div class="back" @click="goBack"></div>

    <!-- 图片容器 -->
    <div class="image-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <van-loading size="24" color="#1989fa">加载中...</van-loading>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <van-icon name="warning-o" size="48" color="#ee0a24" />
        <p class="text-16 text-gray-600 mt-8">{{ error }}</p>
        <van-button type="primary" size="small" @click="loadImage" class="mt-16">
          重新加载
        </van-button>
      </div>

      <!-- 图片显示 -->
      <img
        v-else-if="imageUrl"
        :src="imageUrl"
        :alt="alt"
        class="scrollable-image"
        @load="onImageLoad"
        @error="onImageError"
      />
    </div>

    <!-- 插槽：用于各个页面添加自定义内容 -->
    <slot name="content" :imageLoaded="!loading && !error && imageUrl"></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface Props {
  imagePath: string
  alt?: string
}

const props = withDefaults(defineProps<Props>(), {
  alt: '区域图片'
})

// Emits
const emit = defineEmits<{
  imageLoad: [url: string]
  imageError: [error: string]
}>()

// 响应式数据
const loading = ref(true)
const error = ref('')
const imageUrl = ref('')
const router = useRouter()

// 加载图片
const loadImage = async () => {
  try {
    loading.value = true
    error.value = ''

    // 动态导入图片
    const imageModule = await import(/* @vite-ignore */ props.imagePath)
    imageUrl.value = imageModule.default

    console.log('图片加载成功:', imageUrl.value)
  } catch (err) {
    console.error('加载图片失败:', err)
    error.value = '图片加载失败'
    emit('imageError', '图片加载失败')
  } finally {
    loading.value = false
  }
}

// 图片加载成功
const onImageLoad = () => {
  console.log('图片显示成功')
  emit('imageLoad', imageUrl.value)
}

// 图片加载失败
const onImageError = () => {
  console.error('图片显示失败')
  error.value = '图片显示失败'
  emit('imageError', '图片显示失败')
}

// 返回首页
const goBack = () => {
  router.push('/')
}

// 监听imagePath变化
watch(() => props.imagePath, () => {
  loadImage()
}, { immediate: true })

// 组件挂载时加载图片
onMounted(() => {
  loadImage()
})
</script>

<style lang="scss" scoped>
.scrollable-image-view {
  min-height: 100vh;
  background-color: #000;
  position: relative;
}

.image-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;  /* 为返回按钮提供定位上下文 */
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40px 20px;
  text-align: center;
}

.scrollable-image {
  width: 100%;
  height: auto;
  display: block;
  object-fit: contain;
  max-width: 100%;

  // 确保图片保持原始纵横比
  aspect-ratio: auto;

  // 如果图片很高，允许页面滚动
  min-height: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .scrollable-image {
    width: 100%;
  }
}

.back {
  position: absolute;
  z-index: 10;
  left: calc(20 / 375) * 100%;
  top: calc(39 / 812) * 100vh;
  width: calc(40 / 375) * 100vw;
  height: calc(21 / 812) * 100vh;
  border: 1px solid yellow;
}
</style>