{"name": "wwqy-h5", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^13.6.0", "axios": "^1.11.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vant": "^4.9.21", "vue": "^3.5.18", "vue-router": "^4.5.1", "weixin-js-sdk": "^1.6.5", "reset-css": "^5.0.2"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@unocss/reset": "^66.4.1", "@unocss/preset-rem-to-px": "^66.4.1", "@unocss/preset-wind": "^66.4.1", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "fast-glob": "^3.3.3", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "sass": "^1.89.2", "terser": "^5.43.1", "typescript": "~5.9.2", "unocss": "^66.4.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vconsole": "^3.15.1", "vite": "^7.0.6", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^3.0.5"}}