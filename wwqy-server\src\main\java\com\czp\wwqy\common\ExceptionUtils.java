package com.czp.wwqy.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ExceptionUtils {
    public static final Logger log = LoggerFactory.getLogger(ExceptionUtils.class);

    public static Throwable getCause(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        Throwable cause = throwable.getCause();
        if (cause == null) {
            return throwable;
        }
        return cause;
    }

    public static List<Throwable> getThrowableList(Throwable throwable) {
        List<Throwable> list = new ArrayList<>();
        while (throwable != null && !list.contains(throwable)) {
            list.add(throwable);
            throwable = getCause(throwable);
        }
        return list;
    }

    @SuppressWarnings("unchecked")
    public static <T extends Throwable> T getCauseByType(Throwable throwable, Class<T> clazz) {
        if (clazz == null) {
            return null;
        }
        Set<Throwable> set = new HashSet<>();
        while (throwable != null && !set.contains(throwable)) {
            if (clazz.isInstance(throwable)) {
                return (T) throwable;
            }
            set.add(throwable);
            throwable = getCause(throwable);
        }
        return null;
    }

    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter aw = new PrintWriter(sw, true);
        throwable.printStackTrace(aw);
        return sw.getBuffer().toString();
    }


}