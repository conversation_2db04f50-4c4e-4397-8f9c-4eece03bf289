package com.czp.wwqy.bus.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.czp.wwqy.bus.dto.CheckinListRequest;
import com.czp.wwqy.bus.dto.CheckinRequest;
import com.czp.wwqy.bus.dto.CheckinResponse;
import com.czp.wwqy.bus.entity.CheckinMap;
import com.czp.wwqy.bus.entity.WxUser;
import com.czp.wwqy.bus.mapper.CheckinMapMapper;
import com.czp.wwqy.common.AppException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 打卡地图服务类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CheckinMapService extends ServiceImpl<CheckinMapMapper, CheckinMap> {

    private final WxUserService wxUserService;

    /**
     * 用户打卡
     *
     * @param request 打卡请求
     * @return 打卡响应
     */
    public CheckinResponse checkin(CheckinRequest request) {
        // 根据OpenID获取用户信息
        WxUser wxUser = wxUserService.getByOpenid(request.getOpenid());
        if (wxUser == null) {
            throw AppException.of("用户不存在");
        }

        // 创建打卡记录
        CheckinMap checkinMap = new CheckinMap();
        checkinMap.setUserId(wxUser.getId());
        checkinMap.setLocationName(request.getLocationName());
        checkinMap.setLatitude(request.getLatitude());
        checkinMap.setLongitude(request.getLongitude());
        checkinMap.setCheckinTime(LocalDateTime.now());
        checkinMap.setCreateTime(LocalDateTime.now());
        checkinMap.setUpdateTime(LocalDateTime.now());
        checkinMap.setDeletedFlag(0);

        // 保存打卡记录
        this.save(checkinMap);

        log.info("用户打卡成功，OpenID: {}, 地点: {}", request.getOpenid(), request.getLocationName());

        // 返回打卡响应
        return new CheckinResponse(
                checkinMap.getId(),
                checkinMap.getUserId(),
                checkinMap.getLocationName(),
                checkinMap.getLatitude(),
                checkinMap.getLongitude(),
                checkinMap.getCheckinTime(),
                checkinMap.getCreateTime()
        );
    }

    /**
     * 获取用户打卡记录列表
     *
     * @param request 请求参数
     * @return 打卡记录列表
     */
    public List<CheckinResponse> getCheckinList(CheckinListRequest request) {
        // 根据OpenID获取用户信息
        WxUser wxUser = wxUserService.getByOpenid(request.getOpenid());
        if (wxUser == null) {
            throw AppException.of("用户不存在");
        }

        // 构建查询条件
        LambdaQueryWrapper<CheckinMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CheckinMap::getUserId, wxUser.getId());

        if (StringUtils.hasText(request.getLocationName())) {
            queryWrapper.like(CheckinMap::getLocationName, request.getLocationName());
        }

        queryWrapper.orderByDesc(CheckinMap::getCheckinTime);

        // 查询打卡记录
        List<CheckinMap> checkinMaps = this.list(queryWrapper);

        // 转换为响应DTO
        return checkinMaps.stream()
                .map(checkinMap -> new CheckinResponse(
                        checkinMap.getId(),
                        checkinMap.getUserId(),
                        checkinMap.getLocationName(),
                        checkinMap.getLatitude(),
                        checkinMap.getLongitude(),
                        checkinMap.getCheckinTime(),
                        checkinMap.getCreateTime()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取打卡记录
     *
     * @param userId 用户ID
     * @return 打卡记录列表
     */
    public List<CheckinMap> getByUserId(Long userId) {
        LambdaQueryWrapper<CheckinMap> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CheckinMap::getUserId, userId);
        queryWrapper.orderByDesc(CheckinMap::getCheckinTime);
        return this.list(queryWrapper);
    }
}