# 微信扫码签名问题修复方案

## 问题描述

用户反馈：如果用户已经登录过，进入首页直接显示扫一扫按钮，点击可以正常打开。但是如果用户没有登录，点击微信授权登录之后，打开扫一扫会报 `invalid signature` 错误。

## 问题分析

### 根本原因
微信JSSDK的签名验证是基于完整的URL进行计算的。当用户通过微信授权登录后，URL会发生变化：

**登录前URL（干净）：**
```
https://example.com/
```

**微信授权后URL（带参数）：**
```
https://example.com/?code=xxx&state=xxx
```

由于URL变化，导致JSSDK签名失效，从而出现 `invalid signature` 错误。

### 问题流程
1. 用户访问页面（URL干净）
2. 点击微信授权登录
3. 微信回调带来授权参数（URL变化）
4. 用户登录成功
5. 点击扫一扫功能
6. JSSDK使用带参数的URL请求签名
7. 后端基于带参数的URL计算签名
8. 签名验证失败 → `invalid signature`

## 修复方案

### 1. URL参数清理
在获取JSSDK配置时，自动清理微信授权相关参数：

```typescript
// 修改前
const url = window.location.href.split('#')[0]

// 修改后
let url = window.location.href.split('#')[0]
const urlObj = new URL(url)
urlObj.searchParams.delete('code')
urlObj.searchParams.delete('state')
url = urlObj.toString()
```

### 2. 强制重新初始化
在扫码功能中强制重新初始化JSSDK：

```typescript
static async scanQRCode(): Promise<string> {
  // 强制重新初始化JSSDK以确保签名正确
  WechatUtils.reset()
  await WechatUtils.initJSSDK()
  // ... 扫码逻辑
}
```

### 3. 登录后清理URL
用户登录成功后，清理URL中的授权参数并重置JSSDK状态：

```typescript
static cleanAuthParams(): void {
  const url = new URL(window.location.href)
  let hasAuthParams = false
  
  if (url.searchParams.has('code')) {
    url.searchParams.delete('code')
    hasAuthParams = true
  }
  
  if (url.searchParams.has('state')) {
    url.searchParams.delete('state')
    hasAuthParams = true
  }
  
  if (hasAuthParams) {
    window.history.replaceState({}, document.title, url.toString())
    console.log('已清理URL中的微信授权参数')
    
    // 清理授权参数后，重置JSSDK状态以便重新初始化
    WechatUtils.reset()
  }
}
```

## 修改的文件

### `h5/src/utils/wechat.ts`

**主要修改：**

1. **扫码功能强制重新初始化**（第38-40行）
   ```typescript
   // 强制重新初始化JSSDK以确保签名正确
   WechatUtils.reset()
   await WechatUtils.initJSSDK()
   ```

2. **URL参数清理逻辑**（第127-135行）
   ```typescript
   // 获取当前URL，移除hash部分和微信授权参数
   let url = window.location.href.split('#')[0]
   
   // 移除微信授权相关参数，避免签名问题
   const urlObj = new URL(url)
   urlObj.searchParams.delete('code')
   urlObj.searchParams.delete('state')
   url = urlObj.toString()
   ```

3. **增强的授权参数清理方法**（第252-275行）
   - 检测是否存在授权参数
   - 清理URL参数
   - 重置JSSDK状态
   - 添加调试日志

## 测试验证

### 测试场景1：已登录用户
1. 用户已登录，直接访问首页
2. 点击扫一扫按钮
3. **预期结果**：正常打开扫码功能

### 测试场景2：未登录用户（修复重点）
1. 用户未登录，访问首页
2. 点击微信授权登录
3. 微信回调，用户登录成功
4. URL参数被自动清理
5. 点击扫一扫按钮
6. **预期结果**：正常打开扫码功能，不再报 `invalid signature`

### 测试场景3：多次操作
1. 用户登录后多次使用扫码功能
2. **预期结果**：每次都能正常工作

## 技术细节

### JSSDK初始化流程
1. 检查微信环境
2. 清理URL中的授权参数
3. 请求后端获取签名配置
4. 配置wx.config()
5. 等待wx.ready()回调

### 签名计算逻辑
- 后端基于传入的URL计算签名
- 前端必须使用相同的URL进行JSSDK配置
- URL参数的任何变化都会导致签名失效

### 调试信息
添加了详细的控制台日志：
- JSSDK初始化使用的URL
- 微信JS-SDK配置成功/失败
- URL参数清理状态

## 收益

1. **解决签名问题**：彻底修复微信授权后扫码失败的问题
2. **提升用户体验**：用户登录后可以正常使用所有微信功能
3. **增强稳定性**：通过强制重新初始化确保JSSDK状态正确
4. **便于调试**：添加详细日志便于问题排查

## 注意事项

1. **URL清理时机**：在用户登录成功后立即清理，避免影响后续功能
2. **JSSDK重置**：清理URL参数后必须重置JSSDK状态
3. **浏览器兼容性**：使用`window.history.replaceState`需要现代浏览器支持
4. **调试模式**：生产环境建议关闭JSSDK的debug模式

修复完成后，用户无论是直接访问还是通过微信授权登录，都能正常使用扫码功能。
