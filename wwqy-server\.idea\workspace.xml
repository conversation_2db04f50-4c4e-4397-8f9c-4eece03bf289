<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="69364995-5b03-4fed-9951-9d2fb9bc1f8c" name="更改" comment="polish">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="PUSH_AUTO_UPDATE" value="true" />
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\work\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30RWT7uboPuhQqPuX3hdCVXg1dj" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.wwqy-server [clean,package].executor": "Run",
    "Maven.wwqy-server [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.WwqyServerApplication.executor": "Debug",
    "database.data.extractors.current.export.id": "JSON-Groovy.json.groovy",
    "git-widget-placeholder": "main",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/projects/MyProjects/250726-无畏契约周年庆/wwqy-server/src/main/resources",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\projects\MyProjects\250726-无畏契约周年庆\wwqy-server\src\main\resources" />
      <recent name="D:\projects\MyProjects\250726-无畏契约周年庆\wwqy-server\src\main\java\com\czp\wwqy" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="WwqyServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="wwqy-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.czp.wwqy.WwqyServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.WwqyServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-bf35d07a577b-intellij.indexing.shared.core-IU-252.23892.409" />
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-IU-252.23892.409" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="69364995-5b03-4fed-9951-9d2fb9bc1f8c" name="更改" comment="" />
      <created>1714276274642</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1714276274642</updated>
      <workItem from="1714276276260" duration="167000" />
      <workItem from="1714276553197" duration="135000" />
      <workItem from="1714276739499" duration="6000" />
      <workItem from="1715927982524" duration="96000" />
      <workItem from="1753590877171" duration="26046000" />
      <workItem from="1753666814134" duration="6129000" />
      <workItem from="1753777439579" duration="505000" />
      <workItem from="1753861403171" duration="4000" />
      <workItem from="1753932704803" duration="602000" />
      <workItem from="1754186754988" duration="2525000" />
      <workItem from="1754316328443" duration="10373000" />
      <workItem from="1754398209692" duration="1229000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <option name="closed" value="true" />
      <created>1753591783785</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753591783785</updated>
    </task>
    <task id="LOCAL-00002" summary="init">
      <option name="closed" value="true" />
      <created>1753607691233</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753607691233</updated>
    </task>
    <task id="LOCAL-00003" summary="polish">
      <option name="closed" value="true" />
      <created>1753622071176</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753622071176</updated>
    </task>
    <task id="LOCAL-00004" summary="refactor(bus): 重构用户相关代码&#10;&#10;- 将 User 实体类重命名为 WxUser，以更准确地反映其用途&#10;- 更新了相关控制器、服务类和 mapper 接口的名称&#10;- 调整了部分方法和变量的命名，以适应新的类名&#10;- 更新了数据库表名和相关 SQL 语句">
      <option name="closed" value="true" />
      <created>1753622278372</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753622278372</updated>
    </task>
    <task id="LOCAL-00005" summary="polish">
      <option name="closed" value="true" />
      <created>1753625491283</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753625491283</updated>
    </task>
    <task id="LOCAL-00006" summary="polish">
      <option name="closed" value="true" />
      <created>1753625899696</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753625899696</updated>
    </task>
    <task id="LOCAL-00007" summary="polish">
      <option name="closed" value="true" />
      <created>1753626762200</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753626762200</updated>
    </task>
    <task id="LOCAL-00008" summary="polish">
      <option name="closed" value="true" />
      <created>1753627710149</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753627710149</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="SHOW_DIRTY_RECURSIVELY" value="true" />
    <option name="MARK_IGNORED_AS_EXCLUDED" value="true" />
    <MESSAGE value="init" />
    <MESSAGE value="refactor(bus): 重构用户相关代码&#10;&#10;- 将 User 实体类重命名为 WxUser，以更准确地反映其用途&#10;- 更新了相关控制器、服务类和 mapper 接口的名称&#10;- 调整了部分方法和变量的命名，以适应新的类名&#10;- 更新了数据库表名和相关 SQL 语句" />
    <MESSAGE value="polish" />
    <option name="LAST_COMMIT_MESSAGE" value="polish" />
  </component>
</project>