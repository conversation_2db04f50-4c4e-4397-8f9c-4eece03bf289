import { type ApiResponse, post } from '@/utils/request'

// 用户信息接口
export interface UserInfo {
  id?: number
  openid: string
  nickname: string
  avatarUrl: string
  gender?: number
  city?: string
  province?: string
  country?: string
  createTime?: string
  updateTime?: string
}

// 获取微信授权URL请求
export interface GetWechatAuthUrlRequest {
  redirectUri: string
  state?: string
}

// 获取微信授权URL响应
export interface GetWechatAuthUrlResponse {
  authUrl: string
}

// 微信授权登录请求
export interface WechatLoginRequest {
  code: string
  state?: string
}

// 微信授权登录响应
export interface WechatLoginResponse {
  userInfo: UserInfo
  token?: string
}

// 保存用户信息请求
export interface SaveUserInfoRequest {
  openid: string
  nickname: string
  avatarUrl: string
  gender?: number
  city?: string
  province?: string
  country?: string
}

// 保存用户信息响应
export interface SaveUserInfoResponse {
  success: boolean
  userInfo: UserInfo
}

// 获取用户信息请求
export interface GetUserInfoRequest {
  openid: string
}

// 获取用户信息响应
export interface GetUserInfoResponse {
  userInfo: UserInfo | null
}

// 获取微信JS-SDK配置请求
export interface GetWechatJsConfigRequest {
  url: string
}

// 获取微信JS-SDK配置响应
export interface GetWechatJsConfigResponse {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
  jsApiList: string[]
}

/**
 * 获取微信扫码ticket
 */
export interface GetQrCodeTicketRequest {
  sceneStr: string
  expireSeconds?: number
}

export interface GetQrCodeTicketResponse {
  ticket: string
  expireSeconds: number
  url: string
}

/**
 * 获取微信授权URL
 */
export const GetWechatAuthUrlApi = (data: GetWechatAuthUrlRequest) => {
  return post<GetWechatAuthUrlResponse>('/api/wechat/auth-url', data)
}

/**
 * 微信授权登录
 */
export const WechatLoginApi = (data: WechatLoginRequest) => {
  return post<WechatLoginResponse>('/api/wechat/login', data, {
    showLoading: true
  })
}

/**
 * 保存用户信息
 */
export const SaveUserInfoApi = (data: SaveUserInfoRequest) => {
  return post<SaveUserInfoResponse>('/api/user/save', data)
}

/**
 * 获取用户信息
 */
export const GetUserInfoApi = (data: GetUserInfoRequest) => {
  return post<GetUserInfoResponse>('/api/user/info', data)
}



/**
 * 获取微信JS-SDK配置
 */
export const GetWechatJsConfigApi = (data: GetWechatJsConfigRequest): Promise<ApiResponse<GetWechatJsConfigResponse>> => {
  return post<GetWechatJsConfigResponse>('/api/wechat/config', data)
}

/**
 * 获取微信扫码ticket
 */
export const GetQrCodeTicketApi = (data: GetQrCodeTicketRequest) => {
  return post<GetQrCodeTicketResponse>('/wechat/qrcode-ticket', data)
}