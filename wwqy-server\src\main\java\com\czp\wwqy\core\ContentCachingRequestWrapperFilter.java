package com.czp.wwqy.core;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.atomic.AtomicBoolean;


/**
 * 用于解决请求体只能读取一次的问题
 * 顺序控制要看你自己的代码
 * 尽量小，比如说我这里是OrderedFilter.REQUEST_WRAPPER_FILTER_MAX_ORDER-106
 * REQUEST_WRAPPER_FILTER_MAX_ORDER变量是spring 官方推荐的顺序
 * 但是直接使用可能也会有坑，你可以自己查一下。
 * 因为有一个spring boot 默认扩展的过滤OrderedRequestContextFilter
 * 它使用的是REQUEST_WRAPPER_FILTER_MAX_ORDER - 105
 * 所以为了尽可能早一点，你自己根据你的情况调整顺序
 */
public class ContentCachingRequestWrapperFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        filterChain.doFilter(new PerfectContentCachingRequestWrapper(request), response);
    }


    static class PerfectContentCachingRequestWrapper extends ContentCachingRequestWrapper {

		//原子变量，用来区分首次读取还是非首次
		private final AtomicBoolean isFirst = new AtomicBoolean(true);

		public PerfectContentCachingRequestWrapper(HttpServletRequest request) {
			super(request);
		}

		public PerfectContentCachingRequestWrapper(HttpServletRequest request, int contentCacheLimit) {
			super(request, contentCacheLimit);
		}

		@Override
		public ServletInputStream getInputStream() throws IOException {

			if (isFirst.get()) {
				//首次读取直接调父类的方法，这一次执行完之后 缓存流中有数据了
				//后续读取就读缓存流里的。
				isFirst.set(false);
				return super.getInputStream();
			}

			//用缓存流构建一个新的输入流
			return new ContentCachingServletInputStream(super.getContentAsByteArray());
		}

		//参考自 DelegatingServletInputStream
		static class ContentCachingServletInputStream extends ServletInputStream {

			private final InputStream sourceStream;

			private boolean finished = false;


			public ContentCachingServletInputStream(byte[] bytes) {
				this.sourceStream = new ByteArrayInputStream(bytes);
			}


			@Override
			public int read() throws IOException {
				int data = this.sourceStream.read();
				if (data == -1) {
					this.finished = true;
				}
				return data;
			}

			@Override
			public int available() throws IOException {
				return this.sourceStream.available();
			}

			@Override
			public void close() throws IOException {
				super.close();
				this.sourceStream.close();
			}

			@Override
			public boolean isFinished() {
				return this.finished;
			}

			@Override
			public boolean isReady() {
				return true;
			}

			@Override
			public void setReadListener(ReadListener readListener) {
				throw new UnsupportedOperationException();
			}
		}

	}

}