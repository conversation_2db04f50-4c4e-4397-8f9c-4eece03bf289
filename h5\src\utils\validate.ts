/**
 * 验证工具函数
 * 提供常用的数据验证方法
 */

/**
 * 验证结果接口
 */
export interface ValidateResult {
  valid: boolean
  message?: string
}

/**
 * 验证规则接口
 */
export interface ValidateRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

/**
 * 常用正则表达式
 */
export const REGEX = {
  // 手机号（中国大陆）
  PHONE: /^1[3-9]\d{9}$/,
  // 邮箱
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  // 身份证号
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  // 中文姓名
  CHINESE_NAME: /^[\u4e00-\u9fa5]{2,8}$/,
  // 密码（8-20位，包含字母和数字）
  PASSWORD: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,20}$/,
  // 网址
  URL: /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i,
  // IP地址
  IP: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
  // 车牌号
  LICENSE_PLATE: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/,
  // 银行卡号
  BANK_CARD: /^[1-9]\d{12,19}$/,
  // 微信号
  WECHAT_ID: /^[a-zA-Z][-_a-zA-Z0-9]{5,19}$/,
  // QQ号
  QQ: /^[1-9][0-9]{4,10}$/
}

/**
 * 检查值是否为空
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 验证手机号
 */
export const validatePhone = (phone: string): ValidateResult => {
  if (isEmpty(phone)) {
    return { valid: false, message: '请输入手机号' }
  }
  if (!REGEX.PHONE.test(phone)) {
    return { valid: false, message: '请输入正确的手机号' }
  }
  return { valid: true }
}

/**
 * 验证邮箱
 */
export const validateEmail = (email: string): ValidateResult => {
  if (isEmpty(email)) {
    return { valid: false, message: '请输入邮箱地址' }
  }
  if (!REGEX.EMAIL.test(email)) {
    return { valid: false, message: '请输入正确的邮箱地址' }
  }
  return { valid: true }
}

/**
 * 验证身份证号
 */
export const validateIdCard = (idCard: string): ValidateResult => {
  if (isEmpty(idCard)) {
    return { valid: false, message: '请输入身份证号' }
  }
  if (!REGEX.ID_CARD.test(idCard)) {
    return { valid: false, message: '请输入正确的身份证号' }
  }
  return { valid: true }
}

/**
 * 验证中文姓名
 */
export const validateChineseName = (name: string): ValidateResult => {
  if (isEmpty(name)) {
    return { valid: false, message: '请输入姓名' }
  }
  if (!REGEX.CHINESE_NAME.test(name)) {
    return { valid: false, message: '请输入正确的中文姓名（2-8个汉字）' }
  }
  return { valid: true }
}

/**
 * 验证密码
 */
export const validatePassword = (password: string): ValidateResult => {
  if (isEmpty(password)) {
    return { valid: false, message: '请输入密码' }
  }
  if (password.length < 8) {
    return { valid: false, message: '密码长度不能少于8位' }
  }
  if (password.length > 20) {
    return { valid: false, message: '密码长度不能超过20位' }
  }
  if (!REGEX.PASSWORD.test(password)) {
    return { valid: false, message: '密码必须包含字母和数字' }
  }
  return { valid: true }
}

/**
 * 验证确认密码
 */
export const validateConfirmPassword = (password: string, confirmPassword: string): ValidateResult => {
  if (isEmpty(confirmPassword)) {
    return { valid: false, message: '请确认密码' }
  }
  if (password !== confirmPassword) {
    return { valid: false, message: '两次输入的密码不一致' }
  }
  return { valid: true }
}

/**
 * 验证网址
 */
export const validateUrl = (url: string): ValidateResult => {
  if (isEmpty(url)) {
    return { valid: false, message: '请输入网址' }
  }
  if (!REGEX.URL.test(url)) {
    return { valid: false, message: '请输入正确的网址' }
  }
  return { valid: true }
}

/**
 * 验证银行卡号
 */
export const validateBankCard = (cardNumber: string): ValidateResult => {
  if (isEmpty(cardNumber)) {
    return { valid: false, message: '请输入银行卡号' }
  }
  if (!REGEX.BANK_CARD.test(cardNumber)) {
    return { valid: false, message: '请输入正确的银行卡号' }
  }
  return { valid: true }
}

/**
 * 通用验证器
 */
export const validate = (value: any, rules: ValidateRule[]): ValidateResult => {
  for (const rule of rules) {
    // 必填验证
    if (rule.required && isEmpty(value)) {
      return { valid: false, message: rule.message || '此项为必填项' }
    }

    // 如果值为空且非必填，跳过其他验证
    if (isEmpty(value) && !rule.required) {
      continue
    }

    // 最小长度验证
    if (rule.min !== undefined) {
      const length = typeof value === 'string' ? value.length : 0
      if (length < rule.min) {
        return { valid: false, message: rule.message || `最少需要${rule.min}个字符` }
      }
    }

    // 最大长度验证
    if (rule.max !== undefined) {
      const length = typeof value === 'string' ? value.length : 0
      if (length > rule.max) {
        return { valid: false, message: rule.message || `最多只能输入${rule.max}个字符` }
      }
    }

    // 正则验证
    if (rule.pattern && typeof value === 'string') {
      if (!rule.pattern.test(value)) {
        return { valid: false, message: rule.message || '格式不正确' }
      }
    }

    // 自定义验证器
    if (rule.validator) {
      const result = rule.validator(value)
      if (result !== true) {
        return { 
          valid: false, 
          message: typeof result === 'string' ? result : (rule.message || '验证失败') 
        }
      }
    }
  }

  return { valid: true }
}

/**
 * 验证表单数据
 */
export const validateForm = (data: Record<string, any>, rules: Record<string, ValidateRule[]>): {
  valid: boolean
  errors: Record<string, string>
  firstError?: string
} => {
  const errors: Record<string, string> = {}
  let firstError: string | undefined

  for (const [field, fieldRules] of Object.entries(rules)) {
    const result = validate(data[field], fieldRules)
    if (!result.valid && result.message) {
      errors[field] = result.message
      if (!firstError) {
        firstError = result.message
      }
    }
  }

  return {
    valid: Object.keys(errors).length === 0,
    errors,
    firstError
  }
}

export default {
  isEmpty,
  validatePhone,
  validateEmail,
  validateIdCard,
  validateChineseName,
  validatePassword,
  validateConfirmPassword,
  validateUrl,
  validateBankCard,
  validate,
  validateForm,
  REGEX
}
