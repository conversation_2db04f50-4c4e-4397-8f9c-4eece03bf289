#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\projects\MyProjects\250726-无畏契约周年庆\h5\node_modules\.pnpm\typescript@5.9.2\node_modules\typescript\bin\node_modules;D:\projects\MyProjects\250726-无畏契约周年庆\h5\node_modules\.pnpm\typescript@5.9.2\node_modules\typescript\node_modules;D:\projects\MyProjects\250726-无畏契约周年庆\h5\node_modules\.pnpm\typescript@5.9.2\node_modules;D:\projects\MyProjects\250726-无畏契约周年庆\h5\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/projects/MyProjects/250726-无畏契约周年庆/h5/node_modules/.pnpm/typescript@5.9.2/node_modules/typescript/bin/node_modules:/mnt/d/projects/MyProjects/250726-无畏契约周年庆/h5/node_modules/.pnpm/typescript@5.9.2/node_modules/typescript/node_modules:/mnt/d/projects/MyProjects/250726-无畏契约周年庆/h5/node_modules/.pnpm/typescript@5.9.2/node_modules:/mnt/d/projects/MyProjects/250726-无畏契约周年庆/h5/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../.pnpm/typescript@5.9.2/node_modules/typescript/bin/tsserver" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../.pnpm/typescript@5.9.2/node_modules/typescript/bin/tsserver" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../.pnpm/typescript@5.9.2/node_modules/typescript/bin/tsserver" $args
  } else {
    & "node$exe"  "$basedir/../.pnpm/typescript@5.9.2/node_modules/typescript/bin/tsserver" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
