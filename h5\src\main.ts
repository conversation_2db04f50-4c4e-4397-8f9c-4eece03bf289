// 样式引入
import 'uno.css'
import './styles/global.scss'
import 'vant/lib/index.css'

// 重置浏览器默认样式
import '@unocss/reset/normalize.css'
import 'reset-css/reset.css'

// Vant触摸模拟器（用于桌面端调试）
import '@vant/touch-emulator'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'

// svg图标注册
import 'virtual:svg-icons-register'

const app = createApp(App)
const pinia = createPinia()

pinia.use(piniaPluginPersistedstate)

app.use(pinia)
app.use(router)

app.mount('#app')

// ✅ 只在开发环境引入 vconsole
import('./utils/vconsole').then(({ initVConsole }) => initVConsole())