package com.czp.wwqy.bus.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 打卡请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
public class CheckinRequest {

    /**
     * 用户OpenID
     */
    @NotBlank(message = "用户OpenID不能为空")
    private String openid;

    /**
     * 地点名称
     */
    @NotBlank(message = "地点名称不能为空")
    private String locationName;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;
}