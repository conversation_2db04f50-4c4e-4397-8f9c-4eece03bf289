/**
 * 本地存储工具类
 * 提供类型安全的localStorage和sessionStorage操作
 */

// 存储类型枚举
export enum StorageType {
  LOCAL = 'localStorage',
  SESSION = 'sessionStorage'
}

// 存储项接口
export interface StorageItem<T = any> {
  value: T
  expire?: number
  timestamp: number
}

/**
 * 存储工具类
 */
class StorageUtil {
  private storage: Storage

  constructor(type: StorageType = StorageType.LOCAL) {
    this.storage = window[type]
  }

  /**
   * 设置存储项
   * @param key 键名
   * @param value 值
   * @param expire 过期时间（毫秒），可选
   */
  set<T>(key: string, value: T, expire?: number): void {
    try {
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        expire: expire ? Date.now() + expire : undefined
      }
      this.storage.setItem(key, JSON.stringify(item))
    } catch (error) {
      console.error(`设置存储项失败 [${key}]:`, error)
    }
  }

  /**
   * 获取存储项
   * @param key 键名
   * @param defaultValue 默认值
   * @returns 存储的值或默认值
   */
  get<T>(key: string, defaultValue?: T): T | undefined {
    try {
      const itemStr = this.storage.getItem(key)
      if (!itemStr) {
        return defaultValue
      }

      const item: StorageItem<T> = JSON.parse(itemStr)

      // 检查是否过期
      if (item.expire && Date.now() > item.expire) {
        this.remove(key)
        return defaultValue
      }

      return item.value
    } catch (error) {
      console.error(`获取存储项失败 [${key}]:`, error)
      return defaultValue
    }
  }

  /**
   * 移除存储项
   * @param key 键名
   */
  remove(key: string): void {
    try {
      this.storage.removeItem(key)
    } catch (error) {
      console.error(`移除存储项失败 [${key}]:`, error)
    }
  }

  /**
   * 清空所有存储项
   */
  clear(): void {
    try {
      this.storage.clear()
    } catch (error) {
      console.error('清空存储失败:', error)
    }
  }

  /**
   * 检查键是否存在
   * @param key 键名
   * @returns 是否存在
   */
  has(key: string): boolean {
    return this.storage.getItem(key) !== null
  }

  /**
   * 获取所有键名
   * @returns 键名数组
   */
  keys(): string[] {
    const keys: string[] = []
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key) {
        keys.push(key)
      }
    }
    return keys
  }

  /**
   * 获取存储大小（字节）
   * @returns 存储大小
   */
  size(): number {
    let size = 0
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i)
      if (key) {
        const value = this.storage.getItem(key)
        if (value) {
          size += key.length + value.length
        }
      }
    }
    return size
  }

  /**
   * 清理过期的存储项
   */
  clearExpired(): void {
    const keys = this.keys()
    keys.forEach(key => {
      try {
        const itemStr = this.storage.getItem(key)
        if (itemStr) {
          const item: StorageItem = JSON.parse(itemStr)
          if (item.expire && Date.now() > item.expire) {
            this.remove(key)
          }
        }
      } catch (error) {
        // 如果解析失败，可能是非本工具存储的数据，跳过
      }
    })
  }
}

// 创建实例
export const localStorage = new StorageUtil(StorageType.LOCAL)
export const sessionStorage = new StorageUtil(StorageType.SESSION)

// 便捷方法
export const storage = {
  // localStorage 方法
  local: {
    set: <T>(key: string, value: T, expire?: number) => localStorage.set(key, value, expire),
    get: <T>(key: string, defaultValue?: T) => localStorage.get<T>(key, defaultValue),
    remove: (key: string) => localStorage.remove(key),
    clear: () => localStorage.clear(),
    has: (key: string) => localStorage.has(key),
    keys: () => localStorage.keys(),
    size: () => localStorage.size(),
    clearExpired: () => localStorage.clearExpired()
  },

  // sessionStorage 方法
  session: {
    set: <T>(key: string, value: T, expire?: number) => sessionStorage.set(key, value, expire),
    get: <T>(key: string, defaultValue?: T) => sessionStorage.get<T>(key, defaultValue),
    remove: (key: string) => sessionStorage.remove(key),
    clear: () => sessionStorage.clear(),
    has: (key: string) => sessionStorage.has(key),
    keys: () => sessionStorage.keys(),
    size: () => sessionStorage.size(),
    clearExpired: () => sessionStorage.clearExpired()
  }
}

// 用户相关存储键名常量
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER_INFO: 'userInfo',
  WECHAT_REDIRECT_PATH: 'wechat_redirect_path',
  THEME: 'theme',
  LANGUAGE: 'language',
  SETTINGS: 'settings'
} as const

// 简化的导出函数，兼容原有的localStorage使用方式
export const setItem = <T>(key: string, value: T, expire?: number) => localStorage.set(key, value, expire)
export const getItem = <T>(key: string, defaultValue?: T) => localStorage.get<T>(key, defaultValue)
export const removeItem = (key: string) => localStorage.remove(key)

export default storage