package com.czp.wwqy.core;

import com.czp.wwqy.common.Result;
import com.czp.wwqy.utils.JsonUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date Created in 2021/6/7 16:01
 */
@Slf4j
public class PreControllerExceptionInterceptor implements HandlerInterceptor {

    @Override
    public void afterCompletion( HttpServletRequest request,  HttpServletResponse response,  Object handler, Exception ex) throws IOException {
        if (ex != null) {
            log.error("控制器前置异常: {}", ex.getMessage(), ex);
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "*");
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(400);
            // 检查响应是否已经提交
            if (!response.isCommitted()) {
                Result<Object> error = Result.error(ex.getMessage());
                response.getWriter().write(JsonUtils.object2Json(error));
            } else {
                log.warn("响应已提交，不再尝试写入响应");
            }
        }
    }

}