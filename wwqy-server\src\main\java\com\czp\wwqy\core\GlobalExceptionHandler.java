package com.czp.wwqy.core;

import cn.hutool.core.io.unit.DataSizeUtil;
import com.czp.wwqy.common.AppException;
import com.czp.wwqy.common.ErrorCode;
import com.czp.wwqy.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 *
 * 所有异常返回类型统一为 ResponseEntity<Result<?>>
 * 以支持更精确的 HTTP 状态码控制和泛型响应
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RestControllerAdvice
@SuppressWarnings("all")
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {


    /**
     * 重写 Spring MVC 内部异常处理方法
     */
    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatusCode statusCode, WebRequest request) {
        log.error("Spring MVC异常: {}", ex.getMessage(), ex);
        ErrorCode errorCode = getErrorCodeByHttpStatus(statusCode.value());
        Result<?> response = Result.error(errorCode, ex.getMessage());
        return new ResponseEntity<>(response, headers, statusCode);
    }

    /**
     * 重写参数验证异常处理（@Valid + @RequestBody）
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String message = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        log.warn("参数验证失败: {}", message);
        Result<?> response = Result.error(ErrorCode.VALIDATION_ERROR, message);
        return new ResponseEntity<>(response, headers, HttpStatus.BAD_REQUEST);
    }


    /**
     * 重写找不到处理器异常处理（404）
     */
    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(org.springframework.web.servlet.NoHandlerFoundException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String message = "请求的资源不存在: " + ex.getRequestURL();
        log.warn("找不到处理器: {}", message);
        Result<?> response = Result.error(ErrorCode.NOT_FOUND, message);
        return new ResponseEntity<>(response, headers, HttpStatus.NOT_FOUND);
    }

    /**
     * 重写文件上传大小超限异常处理
     */
    @Override
    protected ResponseEntity<Object> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String message = "文件大小超过限制: " + DataSizeUtil.format(ex.getMaxUploadSize());
        log.warn("文件上传大小超限: {}", message);
        Result<?> response = Result.error(ErrorCode.BAD_REQUEST, message);
        return new ResponseEntity<>(response, headers, HttpStatus.PAYLOAD_TOO_LARGE);
    }

    /**
     * 重写异步请求超时异常处理
     */
    @Override
    protected ResponseEntity<Object> handleAsyncRequestTimeoutException(org.springframework.web.context.request.async.AsyncRequestTimeoutException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String message = "请求处理超时，请稍后重试";
        log.warn("异步请求超时: {}", ex.getMessage());
        Result<?> response = Result.error(ErrorCode.SERVICE_UNAVAILABLE, message);
        return new ResponseEntity<>(response, headers, HttpStatus.SERVICE_UNAVAILABLE);
    }

    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(AppException.class)
    public ResponseEntity<Result<?>> handleAppException(AppException e) {
        log.warn("业务异常: {}", e.getFinalMessage(), e);
        return buildResponse(e.getErrorCode(), e.getFinalMessage(), HttpStatus.BAD_REQUEST);
    }

    /**
     * 处理参数绑定失败（@Validated + 表单参数）
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<Result<?>> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.joining(", "));
        log.warn("参数绑定失败: {}", message);
        return buildResponse(ErrorCode.BAD_REQUEST, message, HttpStatus.BAD_REQUEST);
    }

    /**
     * 非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Result<?>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("参数异常: {}", e.getMessage());
        return buildResponse(ErrorCode.BAD_REQUEST, e.getMessage(), HttpStatus.BAD_REQUEST);
    }

    /**
     * 未捕获异常（兜底处理）
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Result<?>> handleException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        return buildResponse(ErrorCode.INTERNAL_ERROR, "系统内部错误，请稍后重试", HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private ErrorCode getErrorCodeByHttpStatus(int httpStatus) {
        return switch (httpStatus) {
            case 400 -> ErrorCode.BAD_REQUEST;
            case 401 -> ErrorCode.UNAUTHORIZED;
            case 403 -> ErrorCode.FORBIDDEN;
            case 404 -> ErrorCode.NOT_FOUND;
            case 405 -> ErrorCode.METHOD_NOT_ALLOWED;
            case 500 -> ErrorCode.INTERNAL_ERROR;
            case 503 -> ErrorCode.SERVICE_UNAVAILABLE;
            default -> ErrorCode.INTERNAL_ERROR;
        };
    }

    private ResponseEntity<Result<?>> buildResponse(ErrorCode code, String message, HttpStatus status) {
        return new ResponseEntity<>(Result.error(code, message), status);
    }
}