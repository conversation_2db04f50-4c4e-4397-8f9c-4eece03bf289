package com.czp.wwqy.config;

import com.czp.wwqy.core.ContentCachingRequestWrapperFilter;
import com.czp.wwqy.core.FaviconInterceptor;
import com.czp.wwqy.core.PreControllerExceptionInterceptor;
import org.apache.tomcat.util.http.Rfc6265CookieProcessor;
import org.apache.tomcat.util.http.SameSiteCookies;
import org.springframework.boot.web.embedded.tomcat.TomcatContextCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.filter.OrderedFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class MvcConfig implements WebMvcConfigurer {

    public static final int FILTER_ORDER = OrderedFilter.REQUEST_WRAPPER_FILTER_MAX_ORDER - 106;


    /**
     * 配置路径匹配规则,沿用spring5.x的规则, 默认不忽略尾部斜杠
     * <p>
     * 例如：/some/path/ 和 /some/path 都能匹配
     *
     * @param configurer
     */
    @Override
    @SuppressWarnings("deprecation")
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.setUseTrailingSlashMatch(true);
    }

    /**
     * 配置CORS跨域请求
     * 允许前端应用访问后端API
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new PreControllerExceptionInterceptor()).addPathPatterns("/**");
        registry.addInterceptor(new FaviconInterceptor()).addPathPatterns("/favicon.ico");
    }

    @Bean
    public TomcatContextCustomizer sameSiteCookiesConfig() {
        return context -> {
            final Rfc6265CookieProcessor cookieProcessor = new Rfc6265CookieProcessor();
            cookieProcessor.setSameSiteCookies(SameSiteCookies.STRICT.getValue());
            context.setCookieProcessor(cookieProcessor);
        };
    }


    @Bean
    public FilterRegistrationBean<ContentCachingRequestWrapperFilter> RepeatableFilterRegistration() {
        FilterRegistrationBean<ContentCachingRequestWrapperFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new ContentCachingRequestWrapperFilter());
        registration.setName("ContentCachingRequestWrapperFilter");
        registration.setOrder(FILTER_ORDER);
        return registration;
    }
}