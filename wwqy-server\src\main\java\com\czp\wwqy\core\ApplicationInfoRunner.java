package com.czp.wwqy.core;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.info.BuildProperties;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.Ordered;
import org.springframework.core.env.Environment;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date Created in 2021-4-12 17:37
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationInfoRunner implements CommandLineRunner, EnvironmentAware, Ordered {

    private Environment environment;

    @Nullable
    private final BuildProperties buildProperties;

    @Override
    public void run(String... args) {
        String version = "unknown";
        if (buildProperties != null) {
            version = buildProperties.getVersion();
        }

        String applicationName = environment.getProperty("spring.application.name");
        String activeProfile = StrUtil.isBlank(SpringUtil.getActiveProfile()) ? "default" : SpringUtil.getActiveProfile();
        String serverPort = environment.getProperty("server.port");

        String printInfo = """
                
                =============================================
                %s 启动成功
                当前环境: %s
                启动端口: %s
                应用版本: %s
                =============================================
                """.formatted(applicationName, activeProfile, serverPort, version);
        log.info(printInfo);
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }


    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}