<template>
  <div class="min-h-screen flex items-center justify-center bg">
    <!-- 主要内容 -->
    <div class="px-20 py-40 flex flex-col items-center w-full max-w-400">
      <!-- 标题 -->
      <h1 class="text-32 font-bold text-white mb-32 text-center">无畏契约周年庆</h1>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center mb-32">
        <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-16"></div>
        <p class="text-white text-16">{{ loadingText }}</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center mb-32">
        <div class="text-red-400 text-16 mb-16">{{ error }}</div>
        <van-button type="primary" size="large" round @click="initializeUserState" class="w-full h-50 text-16 font-medium">
          重试
        </van-button>
      </div>

      <!-- 登录按钮 -->
      <div v-else class="w-full max-w-300">
        <van-button
          type="primary"
          size="large"
          round
          :loading="loading"
          @click="handleLogin"
          class="w-full h-50 text-16 font-medium"
        >
          <van-icon name="wechat" class="mr-8" />
          {{ buttonText }}
        </van-button>
      </div>

      <!-- 模拟模式切换 -->
      <div v-if="isDev" class="mt-16 w-full max-w-300">
        <van-button
          :type="isMockModeEnabled ? 'success' : 'default'"
          size="small"
          round
          @click="toggleMockMode"
          class="w-full h-40 text-14"
        >
          {{ isMockModeEnabled ? '模拟模式已启用' : '启用模拟模式' }}
        </van-button>
      </div>

      <!-- 调试信息 -->
      <div v-if="isDev" class="mt-32 p-16 bg-black bg-opacity-20 rounded-8 text-left w-full max-w-300">
        <p class="text-12 text-white text-opacity-60 mb-8">调试信息（仅开发环境显示）</p>
        <p class="text-12 text-white text-opacity-80 my-4">微信环境: {{ isWechat() ? '是' : '否' }}</p>
        <p class="text-12 text-white text-opacity-80 my-4">登录状态: {{ userStore.isLoggedIn ? '已登录' : '未登录' }}</p>
        <p class="text-12 text-white text-opacity-80 my-4">模拟模式: {{ isMockModeEnabled ? '启用' : '禁用' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { GetWechatAuthUrlApi, WechatLoginApi } from '@/api/user'
import {
  cleanWechatParams,
  generateRandomString,
  getWechatCode,
  getWechatState,
  isWechat,
  redirectToWechatAuth,
} from '@/utils/wechat'
import { isMockMode, mockDelay, mockLoginResponse, setMockMode } from '@/utils/mock'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const loadingText = ref('')
const error = ref('')
const countdown = ref(0)

// 开发环境标识
const isDev = import.meta.env.DEV

// 模拟模式状态
const isMockModeEnabled = ref(isMockMode())

// 按钮文本
const buttonText = computed(() => {
  if (loading.value) return loadingText.value
  if (userStore.isLoggedIn && countdown.value > 0) {
    return `已登录，${countdown.value}秒后跳转`
  }
  if (userStore.isLoggedIn) return '已登录，点击跳转'
  return '微信授权登录'
})

// 切换模拟模式
const toggleMockMode = () => {
  const newMode = !isMockModeEnabled.value
  setMockMode(newMode)
  isMockModeEnabled.value = newMode
  showToast({
    message: `模拟模式已${newMode ? '启用' : '禁用'}`,
    type: 'success',
  })
}

// 处理登录按钮点击
const handleLogin = async () => {
  if (userStore.isLoggedIn) {
    // 已登录，跳转到首页
    await router.push('/')
    return
  }

  // 未登录，执行微信授权登录
  if (isMockModeEnabled.value) {
    await handleMockLogin()
  } else {
    await handleWechatLogin()
  }
}

// 处理模拟登录
const handleMockLogin = async () => {
  try {
    loading.value = true
    loadingText.value = '模拟登录中...'
    error.value = ''

    // 模拟网络延迟
    await mockDelay(1500)

    // 使用模拟数据登录
    console.log('使用模拟数据登录:', mockLoginResponse)

    userStore.setUserInfo(mockLoginResponse.data.userInfo, 30) // 30天有效期

    showToast({
      message: '模拟登录成功',
      type: 'success',
    })

    // 登录成功后开始倒计时跳转
    startCountdown()
  } catch (err) {
    console.error('模拟登录失败:', err)
    error.value = '模拟登录失败，请重试'
  } finally {
    loading.value = false
    loadingText.value = ''
  }
}

// 处理微信登录
const handleWechatLogin = async () => {
  if (!isWechat()) {
    showToast('请在微信中打开')
    return
  }

  try {
    loading.value = true
    loadingText.value = '获取授权链接...'
    error.value = ''

    // 生成state参数
    const state = generateRandomString()
    // 构建回调URI - 回调到登录页面
    const redirectDomain = import.meta.env.VITE_WECHAT_REDIRECT_DOMAIN || window.location.origin
    const redirectUri = redirectDomain + '/login'

    // 获取微信授权URL
    const response = await GetWechatAuthUrlApi({
      redirectUri,
      state,
    })

    // 跳转到微信授权页面
    redirectToWechatAuth(response.data.authUrl)

    // 跳转后重置loading状态
    loading.value = false
    loadingText.value = ''
  } catch (err) {
    console.error('获取授权URL失败:', err)
    error.value = '获取授权链接失败，请重试'
    loading.value = false
    loadingText.value = ''
  }
}

// 处理微信授权回调
const handleWechatCallback = async () => {
  const code = getWechatCode()
  const state = getWechatState()

  if (!code) {
    return
  }

  try {
    loading.value = true
    loadingText.value = '正在登录...'
    error.value = ''

    let response
    if (isMockModeEnabled.value) {
      // 模拟模式：使用模拟数据
      console.log('模拟微信授权回调登录')
      await mockDelay(1000)
      response = mockLoginResponse
    } else {
      // 真实模式：调用登录接口
      response = await WechatLoginApi({ code, state: state || undefined })
    }

    console.log('登录成功:', response)

    userStore.setUserInfo(response.data.userInfo, 30) // 30天有效期
    showToast({
      message: '登录成功',
      type: 'success',
    })

    // 登录成功后开始倒计时跳转
    startCountdown()
  } catch (err) {
    console.error('微信登录失败:', err)
    error.value = '登录失败，请重试'
  } finally {
    // 清理URL参数
    cleanWechatParams()
    loading.value = false
    loadingText.value = ''
  }
}

// 开始倒计时跳转
const startCountdown = () => {
  countdown.value = 3
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      router.push('/')
    }
  }, 1000)
}

// 初始化用户状态
const initializeUserState = async () => {
  try {
    // 更新模拟模式状态
    isMockModeEnabled.value = isMockMode()

    // 1. 优先检查URL是否有code参数（微信授权回调）
    const code = getWechatCode()
    if (code) {
      console.log('检测到微信授权回调，执行登录流程')
      await handleWechatCallback()
      return
    }

    // 2. 检查pinia中是否有缓存的用户信息
    if (userStore.isLoggedIn) {
      console.log('用户已登录，开始倒计时跳转')
      startCountdown()
      return
    }
  } catch (error) {
    console.error('初始化用户状态失败:', error)
    userStore.clearUserInfo()
  }
}

onMounted(async () => {
  await initializeUserState()
})
</script>

<style lang="scss" scoped>
.bg {
  background: url('@/assets/images/1.jpg') no-repeat center center / 100% 100%;
}
</style>