# 用户登录状态持久化测试指南

## 测试目的
验证用户登录状态在微信环境下的持久化效果，确保用户不需要重复登录。

## 测试环境准备

### 1. 启动开发服务器
```bash
# 前端
cd h5
pnpm run dev

# 后端
cd wwqy-server
mvn spring-boot:run
```

### 2. 配置内网穿透
确保natapp配置正确，能够通过外网访问本地服务。

## 测试步骤

### 测试1：首次登录和状态保存
1. **清理缓存**：
   - 打开浏览器开发者工具
   - 清理localStorage: `localStorage.clear()`
   - 刷新页面

2. **首次登录**：
   - 在微信中打开页面
   - 点击"微信授权登录"按钮
   - 完成微信授权流程
   - 观察控制台日志，确认用户信息保存成功

3. **验证状态保存**：
   - 检查localStorage中的数据：
     ```javascript
     // 检查pinia持久化数据
     console.log('Pinia数据:', localStorage.getItem('wwqy_user_store'))
     
     // 检查备份数据
     console.log('备份数据:', localStorage.getItem('wwqy_user_backup'))
     ```

### 测试2：页面刷新后状态恢复
1. **刷新页面**：
   - 在登录成功后，直接刷新页面
   - 观察是否需要重新登录
   - 检查控制台日志中的状态恢复信息

2. **预期结果**：
   - 页面刷新后自动恢复登录状态
   - 控制台显示："发现有效的pinia缓存用户信息，跳过登录流程"
   - 用户信息正确显示

### 测试3：关闭页面后重新打开
1. **关闭页面**：
   - 完全关闭微信中的页面
   - 等待几秒钟

2. **重新打开**：
   - 在微信中重新打开页面链接
   - 观察是否需要重新登录
   - 检查控制台日志

3. **预期结果**：
   - 重新打开后自动恢复登录状态
   - 不需要重新进行微信授权
   - 显示用户信息和扫码按钮

### 测试4：pinia持久化失效时的备份恢复
1. **模拟pinia失效**：
   - 在开发者工具中删除pinia数据：
     ```javascript
     localStorage.removeItem('wwqy_user_store')
     ```
   - 保留备份数据
   - 刷新页面

2. **预期结果**：
   - 控制台显示："pinia缓存无效，尝试从localStorage恢复"
   - 控制台显示："从localStorage恢复用户信息成功"
   - 用户状态正常恢复

### 测试5：扫码功能测试
1. **登录状态下测试扫码**：
   - 确保已登录
   - 点击"扫一扫"按钮
   - 使用微信扫码功能扫描二维码

2. **预期结果**：
   - 扫码功能正常启动
   - 能够获取扫码结果
   - 控制台显示扫码结果信息

### 测试6：登录过期测试
1. **修改过期时间**（仅测试用）：
   - 在用户store中临时修改过期时间为较短时间（如1分钟）
   - 登录后等待过期
   - 刷新页面

2. **预期结果**：
   - 过期后自动清理登录状态
   - 控制台显示："用户登录状态已过期，自动清理"
   - 显示登录按钮

## 调试信息查看

### 控制台日志关键信息
```javascript
// 查看用户状态摘要
console.log('用户状态:', userStore.getUserSummary())

// 查看localStorage数据
console.log('Pinia持久化:', localStorage.getItem('wwqy_user_store'))
console.log('备份数据:', localStorage.getItem('wwqy_user_backup'))

// 查看登录状态
console.log('是否登录:', userStore.isLoggedIn)
console.log('剩余天数:', userStore.remainingDays)
```

### 开发环境调试面板
在登录状态下，页面底部会显示调试信息：
- 登录剩余天数
- OpenID后8位
- 用户昵称

## 常见问题排查

### 问题1：页面刷新后需要重新登录
**可能原因**：
- localStorage被清理
- pinia持久化配置问题
- 用户信息验证过于严格

**排查步骤**：
1. 检查localStorage中是否有数据
2. 检查控制台是否有错误信息
3. 查看用户信息验证逻辑

### 问题2：扫码功能无法使用
**可能原因**：
- 微信JSSDK未正确初始化
- 不在微信环境中
- 用户未登录

**排查步骤**：
1. 确认在微信中打开页面
2. 检查JSSDK初始化日志
3. 确认用户已登录

### 问题3：登录状态异常
**可能原因**：
- 时间戳异常
- 数据格式错误
- 序列化/反序列化问题

**排查步骤**：
1. 查看用户状态摘要
2. 检查localStorage数据格式
3. 查看序列化日志

## 测试通过标准

✅ **所有测试通过的标准**：
1. 首次登录成功，用户信息正确保存
2. 页面刷新后自动恢复登录状态
3. 关闭页面重新打开后保持登录状态
4. pinia失效时能从备份恢复
5. 扫码功能在登录状态下正常工作
6. 登录过期后能正确清理状态
7. 调试信息显示正确
8. 无控制台错误信息

## 性能优化建议

1. **缓存策略**：
   - 使用双重缓存（pinia + localStorage）
   - 定期清理过期数据

2. **用户体验**：
   - 添加加载状态提示
   - 优化错误处理
   - 提供调试信息

3. **安全性**：
   - 设置合理的过期时间
   - 验证用户信息完整性
   - 处理异常情况

通过以上测试，可以确保用户登录状态持久化功能正常工作，提升用户体验。
