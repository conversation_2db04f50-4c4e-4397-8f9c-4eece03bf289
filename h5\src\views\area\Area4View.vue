<template>
  <div class="area4-view">

    <!-- 可滚动图片组件 -->
    <ScrollableImageView
      :image-url="area4Image"
      alt="打卡地图"
      @image-load="onImageLoad"
      @image-error="onImageError"
    >
      <!-- 区域4特有的功能内容 -->
      <template #content="{ imageLoaded }">
        <div v-if="imageLoaded" class="area4-content">
          <!-- 这里可以添加区域4特有的交互功能 -->
          <!-- 例如：地图打卡、位置标记等 -->
          <div class="floating-actions">
            <!-- 示例：区域4特有功能按钮 -->
            <!-- <van-button type="info" @click="handleArea4Action">
              地图打卡
            </van-button> -->
          </div>
        </div>
      </template>
    </ScrollableImageView>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import ScrollableImageView from '@/components/ScrollableImageView.vue'
import area4Image from '@/assets/images/area/area4.jpg'

const router = useRouter()

// 返回首页
const goBack = () => {
  router.push('/')
}

// 图片加载成功回调
const onImageLoad = (url: string) => {
  console.log('区域4图片加载成功:', url)
  // 这里可以添加区域4特有的初始化逻辑
}

// 图片加载失败回调
const onImageError = (error: string) => {
  console.error('区域4图片加载失败:', error)
}

// 区域4特有的功能方法
const handleArea4Action = () => {
  // 区域4特有的业务逻辑（打卡地图相关）
  console.log('执行区域4特有功能')
}
</script>

<style lang="scss" scoped>
.area4-view {
  position: relative;
  min-height: 100vh;
}

.area4-content {
  position: relative;
  z-index: 5;
}

.floating-actions {
  position: fixed;
  bottom: 40px;
  right: 20px;
  z-index: 10;

  // 可以添加区域4特有的样式（打卡地图主题）
}

// 区域4特有的样式
// 例如：地图相关的交互效果
</style>