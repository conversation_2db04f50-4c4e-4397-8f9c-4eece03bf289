package com.czp.wwqy.core;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 拦截 /favicon.ico 请求
 *
 * <AUTHOR>
 * @date Created in 2021/8/17 12:14
 */
public class FaviconInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return false;
    }

}