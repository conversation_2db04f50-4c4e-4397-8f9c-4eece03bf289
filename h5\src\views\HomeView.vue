<template>
  <div class="relative w-screen h-screen overflow-hidden">
    <!-- 背景图片 -->
    <div class="absolute inset-0 bg"></div>

    <!-- 6个点击区域，根据背景图片中的方块位置定位 -->
    <div class="relative w-full h-full z-2">
      <div v-for="n in 6" :key="n" class="area" :class="`area-${n}`" @click="handleAreaClick(n)" />
    </div>
    <!-- 轮播图区域 -->
    <div class="swipe-area">
      <!-- 轮播图主体 -->
      <van-swipe ref="swipeRef"  :show-indicators="false" class="swipe-main">
        <van-swipe-item v-for="(item, index) in swipeItems" :key="index">
          <div class="swipe-item" :style="{ backgroundColor: item.color }"></div>
        </van-swipe-item>
      </van-swipe>

      <!-- 左侧按钮 -->
      <div class="swipe-prev-btn" @click="prevSlide"></div>
      <!-- 右侧按钮 -->
      <div class="swipe-next-btn" @click="nextSlide"></div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 轮播图相关
const swipeRef = ref()

// 模拟轮播图数据
const swipeItems = ref([{ color: '#ff6b6b' }, { color: '#4ecdc4' }, { color: '#45b7d1' }, { color: '#f9ca24' }])

// 轮播图控制方法
const prevSlide = () => {
  console.log('点击了上一页')

  swipeRef.value?.prev()
}

const nextSlide = () => {
  console.log('点击了下一页')
  swipeRef.value?.next()
}

// 处理区域点击
const handleAreaClick = (index: number) => {
  console.log(`点击了区域 ${index}`)

  // 检查登录状态
  if (!checkLoginStatus()) {
    return
  }

  // area6 跳转到外部链接（暂时跳过）
  if (index === 6) {
    showToast('区域6功能暂未开放')
    return
  }

  showToast(`进入区域 ${index}`)
  router.push(`/area${index}`)
}

// 检查用户登录状态
const checkLoginStatus = () => {
  if (!userStore.isLoggedIn) {
    showToast('请先登录')
    router.push('/login')
    return false
  }
  return true
}

onMounted(() => {
  // 检查登录状态
  if (!checkLoginStatus()) {
    return
  }
  console.log('首页加载完成')
})
</script>

<style lang="scss" scoped>
.bg {
  background: url('@/assets/images/2.jpg') no-repeat center center / 100% 100%;
}

.area {
  position: absolute;
  border: 1px solid red;
}

.area-1 {
  top: 42.6%;
  left: 5.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-2 {
  top: 42.6%;
  left: 35.4%;
  width: calc(110 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-3 {
  top: 42.6%;
  left: 68.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-4 {
  top: 56.1%;
  left: 5.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-5 {
  top: 56.1%;
  left: 35.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-6 {
  top: 56.1%;
  left: 68.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

// 轮播图样式 - 使用相对定位
.swipe-area {
  position: absolute;
  top: 74.6%;
  left: 50%;
  transform: translateX(-50%);
  width: 68.8%;
  height: calc(143 / 812) * 100vh;
  border: 1px solid yellow;
  .swipe-main {
    width: 100%;
    height: 100%;
    display: flex;
    border: 1px solid blue;
  }

  .swipe-item {
    width: 100%;
    height: 100%;
  }
}

.swipe-prev-btn {
  position: absolute;
  left: -16%;
  top: 50%;
  transform: translateY(-50%);
  width: calc(50 / 375) * 100%;
  height: calc(40 / 812) * 100vh;
  background-color: red;
}

.swipe-next-btn {
  position: absolute;
  right: -16%;
  top: 50%;
  transform: translateY(-50%);
  width: calc(52 / 375) * 100%;
  height: calc(40 / 812) * 100vh;
  background-color: red;
}
</style>