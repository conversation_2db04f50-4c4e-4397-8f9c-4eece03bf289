<template>
  <div class="relative w-screen h-screen overflow-hidden">
    <!-- 背景图片 -->
    <div class="absolute inset-0 bg"></div>

    <!-- 6个点击区域，根据背景图片中的方块位置定位 -->
    <div class="relative w-full h-full z-2">
      <div v-for="n in 6" :key="n" class="area" :class="`area-${n}`" @click="handleAreaClick(n)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 处理区域点击
const handleAreaClick = (index: number) => {
  console.log(`点击了区域 ${index}`)

  // 检查登录状态
  if (!checkLoginStatus()) {
    return
  }

  // area6 跳转到外部链接（暂时跳过）
  if (index === 6) {
    showToast('区域6功能暂未开放')
    return
  }

  showToast(`进入区域 ${index}`)
  router.push(`/area${index}`)
}

// 检查用户登录状态
const checkLoginStatus = () => {
  if (!userStore.isLoggedIn) {
    showToast('请先登录')
    router.push('/login')
    return false
  }
  return true
}

onMounted(() => {
  // 检查登录状态
  if (!checkLoginStatus()) {
    return
  }
  console.log('首页加载完成')
})
</script>

<style lang="scss" scoped>
.bg {
  background: url('@/assets/images/2.jpg') no-repeat center center / 100% 100%;
}

.area {
  position: absolute;
  border: 1px solid red;
}

.area-1 {
  top: 42.6%;
  left: 5.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-2 {
  top: 42.6%;
  left: 35.4%;
  width: calc(110 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-3 {
  top: 42.6%;
  left: 68.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-4 {
  top: 56.1%;
  left: 5.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-5 {
  top: 56.1%;
  left: 35.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}

.area-6 {
  top: 56.1%;
  left: 68.4%;
  width: calc(100 / 375) * 100%;
  height: calc(98 / 812) * 100%;
}
</style>