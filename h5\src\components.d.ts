/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollableImageView: typeof import('./components/ScrollableImageView.vue')['default']
    SvgIcon: typeof import('./components/SvgIcon.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanIcon: typeof import('vant/es')['Icon']
    VanLoading: typeof import('vant/es')['Loading']
  }
}
