server:
  port: 8080

spring:
  application:
    name: huawei-server

  datasource:
     driver-class-name: com.mysql.cj.jdbc.Driver
     url: ***********************************************************************************************************************************************************************************************************************************************
     username: root
     password: 123456
     hikari:
      #客户端等待连接池连接的最大毫秒数
      connection-timeout: 60000
      #允许连接在连接池中空闲的最长时间(以毫秒为单位)
      idle-timeout: 30000
      #连接将被测试活动的最大时间量
      validation-timeout: 3000
      #池中连接关闭后的最长生命周期
      max-lifetime: 1800000  # 30分钟
      keepalive-time: 60000  # 10分钟
      #最大池大小
      maximum-pool-size: 100
      #连接池中维护的最小空闲连接数
      minimum-idle: 20
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    generate-ddl: true
    open-in-view: false

logging:
  level:
    org.zalando.logbook: trace

logbook:
  filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  write:
    max-body-size: 4096

# 微信配置
wechat:
  appId: wxdac140cf05115764
  appSecret: d95abff85d77cb791d8daecfae8d610f

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    banner: off
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0