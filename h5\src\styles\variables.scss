// 颜色变量
$primary-color: #1989fa;
$success-color: #07c160;
$warning-color: #ff976a;
$danger-color: #ee0a24;
$text-color: #323233;
$text-color-2: #646566;
$text-color-3: #969799;
$border-color: #ebedf0;
$background-color: #f7f8fa;
$white: #ffffff;
$black: #000000;

// 字体大小
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 间距
$padding-xs: 4px;
$padding-sm: 8px;
$padding-md: 12px;
$padding-lg: 16px;
$padding-xl: 20px;
$padding-xxl: 24px;

// 圆角
$border-radius-sm: 2px;
$border-radius-md: 4px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 阴影
$box-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
$box-shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
$box-shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);

// 层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 动画时间
$animation-duration-fast: 0.2s;
$animation-duration-base: 0.3s;
$animation-duration-slow: 0.5s;

// 移动端适配
$mobile-width: 375px;
$tablet-width: 768px;
$desktop-width: 1024px;
