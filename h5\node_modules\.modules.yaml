hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@8.1.1':
    '@antfu/utils': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.3.0':
    '@iconify/utils': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@quansync/fs@0.1.3':
    '@quansync/fs': private
  '@rolldown/pluginutils@1.0.0-beta.19':
    '@rolldown/pluginutils': private
  '@rolldown/pluginutils@1.0.0-beta.29':
    '@rolldown/pluginutils': private
  '@rollup/pluginutils@5.2.0(rollup@4.45.3)':
    '@rollup/pluginutils': private
  '@rollup/rollup-win32-x64-msvc@4.45.3':
    '@rollup/rollup-win32-x64-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.46.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/node@24.1.0':
    '@types/node': private
  '@types/node@24.2.0':
    '@types/node': private
  '@types/strip-bom@3.0.0':
    '@types/strip-bom': private
  '@types/strip-json-comments@0.0.30':
    '@types/strip-json-comments': private
  '@types/svgo@2.6.4':
    '@types/svgo': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@unocss/astro@66.3.3(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1))(vue@3.5.18(typescript@5.8.3))':
    '@unocss/astro': private
  '@unocss/astro@66.3.3(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2))(vue@3.5.18(typescript@5.8.3))':
    '@unocss/astro': private
  '@unocss/astro@66.3.3(vite@7.0.6(@types/node@24.1.0)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1))(vue@3.5.18(typescript@5.8.3))':
    '@unocss/astro': private
  '@unocss/astro@66.4.1(vite@7.0.6(@types/node@24.2.0)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1))':
    '@unocss/astro': private
  '@unocss/cli@66.3.3':
    '@unocss/cli': private
  '@unocss/cli@66.4.1':
    '@unocss/cli': private
  '@unocss/config@66.3.3':
    '@unocss/config': private
  '@unocss/config@66.4.1':
    '@unocss/config': private
  '@unocss/core@66.3.3':
    '@unocss/core': private
  '@unocss/core@66.4.1':
    '@unocss/core': private
  '@unocss/extractor-arbitrary-variants@66.3.3':
    '@unocss/extractor-arbitrary-variants': private
  '@unocss/extractor-arbitrary-variants@66.4.1':
    '@unocss/extractor-arbitrary-variants': private
  '@unocss/inspector@66.3.3(vue@3.5.18(typescript@5.8.3))':
    '@unocss/inspector': private
  '@unocss/inspector@66.4.1':
    '@unocss/inspector': private
  '@unocss/postcss@66.3.3(postcss@5.2.18)':
    '@unocss/postcss': private
  '@unocss/postcss@66.3.3(postcss@8.5.6)':
    '@unocss/postcss': private
  '@unocss/postcss@66.4.1(postcss@5.2.18)':
    '@unocss/postcss': private
  '@unocss/preset-attributify@66.3.3':
    '@unocss/preset-attributify': private
  '@unocss/preset-attributify@66.4.1':
    '@unocss/preset-attributify': private
  '@unocss/preset-icons@66.3.3':
    '@unocss/preset-icons': private
  '@unocss/preset-icons@66.4.1':
    '@unocss/preset-icons': private
  '@unocss/preset-mini@66.3.3':
    '@unocss/preset-mini': private
  '@unocss/preset-mini@66.4.1':
    '@unocss/preset-mini': private
  '@unocss/preset-tagify@66.3.3':
    '@unocss/preset-tagify': private
  '@unocss/preset-tagify@66.4.1':
    '@unocss/preset-tagify': private
  '@unocss/preset-typography@66.3.3':
    '@unocss/preset-typography': private
  '@unocss/preset-typography@66.4.1':
    '@unocss/preset-typography': private
  '@unocss/preset-uno@66.3.3':
    '@unocss/preset-uno': private
  '@unocss/preset-uno@66.4.1':
    '@unocss/preset-uno': private
  '@unocss/preset-web-fonts@66.3.3':
    '@unocss/preset-web-fonts': private
  '@unocss/preset-web-fonts@66.4.1':
    '@unocss/preset-web-fonts': private
  '@unocss/preset-wind3@66.3.3':
    '@unocss/preset-wind3': private
  '@unocss/preset-wind3@66.4.1':
    '@unocss/preset-wind3': private
  '@unocss/preset-wind4@66.3.3':
    '@unocss/preset-wind4': private
  '@unocss/preset-wind4@66.4.1':
    '@unocss/preset-wind4': private
  '@unocss/preset-wind@66.3.3':
    '@unocss/preset-wind': private
  '@unocss/reset@66.3.3':
    '@unocss/reset': private
  '@unocss/reset@66.4.1':
    '@unocss/reset': private
  '@unocss/rule-utils@66.3.3':
    '@unocss/rule-utils': private
  '@unocss/rule-utils@66.4.1':
    '@unocss/rule-utils': private
  '@unocss/transformer-attributify-jsx@66.3.3':
    '@unocss/transformer-attributify-jsx': private
  '@unocss/transformer-attributify-jsx@66.4.1':
    '@unocss/transformer-attributify-jsx': private
  '@unocss/transformer-compile-class@66.3.3':
    '@unocss/transformer-compile-class': private
  '@unocss/transformer-compile-class@66.4.1':
    '@unocss/transformer-compile-class': private
  '@unocss/transformer-directives@66.3.3':
    '@unocss/transformer-directives': private
  '@unocss/transformer-directives@66.4.1':
    '@unocss/transformer-directives': private
  '@unocss/transformer-variant-group@66.3.3':
    '@unocss/transformer-variant-group': private
  '@unocss/transformer-variant-group@66.4.1':
    '@unocss/transformer-variant-group': private
  '@unocss/vite@66.3.3(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1))(vue@3.5.18(typescript@5.8.3))':
    '@unocss/vite': private
  '@unocss/vite@66.3.3(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2))(vue@3.5.18(typescript@5.8.3))':
    '@unocss/vite': private
  '@unocss/vite@66.3.3(vite@7.0.6(@types/node@24.1.0)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1))(vue@3.5.18(typescript@5.8.3))':
    '@unocss/vite': private
  '@unocss/vite@66.4.1(vite@7.0.6(@types/node@24.2.0)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1))':
    '@unocss/vite': private
  '@vant/popperjs@1.3.0':
    '@vant/popperjs': private
  '@vant/use@1.6.0(vue@3.5.18(typescript@5.8.3))':
    '@vant/use': private
  '@vant/use@1.6.0(vue@3.5.18(typescript@5.9.2))':
    '@vant/use': private
  '@volar/language-core@2.4.15':
    '@volar/language-core': private
  '@volar/language-core@2.4.20':
    '@volar/language-core': private
  '@volar/language-core@2.4.22':
    '@volar/language-core': private
  '@volar/source-map@2.4.15':
    '@volar/source-map': private
  '@volar/source-map@2.4.20':
    '@volar/source-map': private
  '@volar/source-map@2.4.22':
    '@volar/source-map': private
  '@volar/typescript@2.4.15':
    '@volar/typescript': private
  '@volar/typescript@2.4.20':
    '@volar/typescript': private
  '@volar/typescript@2.4.22':
    '@volar/typescript': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.18':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.18':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.18':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.18':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/devtools-api@7.7.7':
    '@vue/devtools-api': private
  '@vue/devtools-core@7.7.7(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1))(vue@3.5.18(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-core@7.7.7(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2))(vue@3.5.18(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-core@7.7.7(vite@7.0.6(@types/node@22.16.5))(vue@3.5.18(typescript@5.8.3))':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': private
  '@vue/language-core@2.2.12(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/language-core@3.0.4(typescript@5.8.3)':
    '@vue/language-core': private
  '@vue/language-core@3.0.5(typescript@5.9.2)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.18':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.18':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.18':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.18(vue@3.5.18(typescript@5.8.3))':
    '@vue/server-renderer': private
  '@vue/server-renderer@3.5.18(vue@3.5.18(typescript@5.9.2))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.18':
    '@vue/shared': private
  '@vueuse/metadata@13.6.0':
    '@vueuse/metadata': private
  '@vueuse/shared@13.6.0(vue@3.5.18(typescript@5.8.3))':
    '@vueuse/shared': private
  '@vueuse/shared@13.6.0(vue@3.5.18(typescript@5.9.2))':
    '@vueuse/shared': private
  abab@2.0.6:
    abab: private
  abbrev@1.1.1:
    abbrev: private
  acorn-globals@7.0.1:
    acorn-globals: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  alien-signals@1.0.13:
    alien-signals: private
  alien-signals@2.0.5:
    alien-signals: private
  alien-signals@2.0.6:
    alien-signals: private
  ansi-regex@2.1.1:
    ansi-regex: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@6.2.1:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  aproba@2.1.0:
    aproba: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  arr-diff@4.0.0:
    arr-diff: private
  arr-flatten@1.1.0:
    arr-flatten: private
  arr-union@3.1.0:
    arr-union: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-unique@0.3.2:
    array-unique: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  assign-symbols@1.0.0:
    assign-symbols: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  balanced-match@1.0.2:
    balanced-match: private
  base@0.11.2:
    base: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  birpc@2.5.0:
    birpc: private
  bluebird@3.7.2:
    bluebird: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  bundle-name@4.1.0:
    bundle-name: private
  cac@6.7.14:
    cac: private
  cache-base@1.0.1:
    cache-base: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  canvas@2.11.2:
    canvas: private
  chalk@1.1.3:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  chokidar@4.0.3:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  class-utils@0.3.6:
    class-utils: private
  clone@2.1.2:
    clone: private
  collection-visit@1.0.0:
    collection-visit: private
  color-support@1.1.3:
    color-support: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.20.3:
    commander: private
  component-emitter@1.3.1:
    component-emitter: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.2:
    consola: private
  console-control-strings@1.1.0:
    console-control-strings: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  copy-descriptor@0.1.1:
    copy-descriptor: private
  copy-text-to-clipboard@3.2.0:
    copy-text-to-clipboard: private
  core-js@3.44.0:
    core-js: private
  cors@2.8.5:
    cors: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-select@4.3.0:
    css-select: private
  css-tree@3.1.0:
    css-tree: private
  css-what@6.2.2:
    css-what: private
  csso@4.2.0:
    csso: private
  cssom@0.5.0:
    cssom: private
  cssstyle@2.3.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  data-urls@3.0.2:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.4.1:
    debug: private
  decimal.js@10.6.0:
    decimal.js: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  decompress-response@4.2.1:
    decompress-response: private
  deep-pick-omit@1.2.1:
    deep-pick-omit: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  define-property@1.0.0:
    define-property: private
  defu@6.1.4:
    defu: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  destr@2.0.5:
    destr: private
  detect-libc@1.0.3:
    detect-libc: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domexception@4.0.0:
    domexception: private
  domhandler@4.3.1:
    domhandler: private
  domutils@2.8.0:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  electron-to-chromium@1.5.191:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  entities@4.5.0:
    entities: private
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@5.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  esprima@4.0.1:
    esprima: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter3@4.0.7:
    eventemitter3: private
  execa@9.6.0:
    execa: private
  expand-brackets@2.1.4:
    expand-brackets: private
  exsolve@1.0.7:
    exsolve: private
  extend-shallow@2.0.1:
    extend-shallow: private
  extglob@2.0.4:
    extglob: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  figures@6.1.0:
    figures: private
  fill-range@4.0.0:
    fill-range: private
  fill-range@7.1.1:
    fill-range: private
  follow-redirects@1.15.11:
    follow-redirects: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  for-in@1.0.2:
    for-in: private
  form-data@4.0.4:
    form-data: private
  fragment-cache@0.2.1:
    fragment-cache: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-extra@11.3.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gauge@3.0.2:
    gauge: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@9.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-value@2.0.6:
    get-value: private
  glob-parent@5.1.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@15.15.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gzip-size@6.0.0:
    gzip-size: private
  has-ansi@2.0.0:
    has-ansi: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@1.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  has-value@1.0.0:
    has-value: private
  has-values@1.0.0:
    has-values: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hookable@5.5.3:
    hookable: private
  html-encoding-sniffer@3.0.0:
    html-encoding-sniffer: private
  htmlparser2@3.10.1:
    htmlparser2: private
  http-proxy-agent@5.0.0:
    http-proxy-agent: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@8.0.1:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  image-size@0.5.5:
    image-size: private
  immutable@5.1.3:
    immutable: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-data-descriptor@1.0.1:
    is-data-descriptor: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-descriptor@1.0.3:
    is-descriptor: private
  is-docker@3.0.0:
    is-docker: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@3.0.0:
    is-number: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@4.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@4.1.16:
    is-what: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@3.1.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isarray@2.0.5:
    isarray: private
  isexe@3.1.1:
    isexe: private
  isobject@3.0.1:
    isobject: private
  jiti@2.5.1:
    jiti: private
  js-base64@2.6.4:
    js-base64: private
  js-tokens@4.0.0:
    js-tokens: private
  js-tokens@9.0.1:
    js-tokens: private
  jsdom@20.0.3(canvas@2.11.2):
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-even-better-errors@4.0.0:
    json-parse-even-better-errors: private
  json5@1.0.2:
    json5: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  kind-of@5.1.0:
    kind-of: private
  kolorist@1.8.0:
    kolorist: private
  loader-utils@1.4.2:
    loader-utils: private
  local-pkg@1.1.1:
    local-pkg: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@3.1.0:
    make-dir: private
  map-cache@0.2.2:
    map-cache: private
  map-visit@1.0.0:
    map-visit: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdn-data@2.0.14:
    mdn-data: private
  mdn-data@2.12.2:
    mdn-data: private
  memorystream@0.3.1:
    memorystream: private
  merge-options@1.0.1:
    merge-options: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-response@2.1.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@5.0.0:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mixin-deep@1.3.2:
    mixin-deep: private
  mkdirp@1.0.4:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  muggle-string@0.4.1:
    muggle-string: private
  mutation-observer@1.0.3:
    mutation-observer: private
  nan@2.23.0:
    nan: private
  nanoid@3.3.11:
    nanoid: private
  nanoid@5.1.5:
    nanoid: private
  nanomatch@1.2.13:
    nanomatch: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-fetch-native@1.6.7:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  nopt@5.0.0:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-normalize-package-bin@4.0.0:
    npm-normalize-package-bin: private
  npm-run-path@6.0.0:
    npm-run-path: private
  npmlog@5.0.1:
    npmlog: private
  nth-check@2.1.1:
    nth-check: private
  nwsapi@2.2.21:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  object-copy@0.1.0:
    object-copy: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-visit@1.0.1:
    object-visit: private
  object.assign@4.1.7:
    object.assign: private
  object.pick@1.3.0:
    object.pick: private
  ofetch@1.4.1:
    ofetch: private
  once@1.4.0:
    once: private
  open@10.2.0:
    open: private
  own-keys@1.0.1:
    own-keys: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  parse-ms@4.0.0:
    parse-ms: private
  parse5@7.3.0:
    parse5: private
  pascalcase@0.1.1:
    pascalcase: private
  path-browserify@1.0.1:
    path-browserify: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  pathe@0.2.0:
    pathe: private
  pathe@2.0.3:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pkg-types@2.2.0:
    pkg-types: private
  posix-character-classes@0.1.1:
    posix-character-classes: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-prefix-selector@1.16.1(postcss@5.2.18):
    postcss-prefix-selector: private
  postcss@8.5.6:
    postcss: private
  posthtml-parser@0.2.1:
    posthtml-parser: private
  posthtml-rename-id@1.0.12:
    posthtml-rename-id: private
  posthtml-render@1.4.0:
    posthtml-render: private
  posthtml-svg-mode@1.0.3:
    posthtml-svg-mode: private
  posthtml@0.9.2:
    posthtml: private
  pretty-ms@9.2.0:
    pretty-ms: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  query-string@4.3.4:
    query-string: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  read-package-json-fast@4.0.0:
    read-package-json-fast: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regex-not@1.0.2:
    regex-not: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  repeat-element@1.1.4:
    repeat-element: private
  repeat-string@1.6.1:
    repeat-string: private
  requires-port@1.0.0:
    requires-port: private
  resolve-url@0.2.1:
    resolve-url: private
  ret@0.1.15:
    ret: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.45.3:
    rollup: private
  rollup@4.46.0:
    rollup: private
  rollup@4.46.2:
    rollup: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-regex@1.1.0:
    safe-regex: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  scule@1.3.0:
    scule: private
  semver@6.3.1:
    semver: private
  semver@7.7.2:
    semver: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  set-value@2.0.1:
    set-value: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@3.1.1:
    simple-get: private
  sirv@3.0.1:
    sirv: private
  snapdragon-node@2.1.1:
    snapdragon-node: private
  snapdragon-util@3.0.1:
    snapdragon-util: private
  snapdragon@0.8.2:
    snapdragon: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-resolve@0.5.3:
    source-map-resolve: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map-url@0.4.1:
    source-map-url: private
  source-map@0.6.1:
    source-map: private
  speakingurl@14.0.1:
    speakingurl: private
  split-string@3.1.0:
    split-string: private
  stable@0.1.8:
    stable: private
  static-extend@0.1.2:
    static-extend: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  strict-uri-encode@1.1.0:
    strict-uri-encode: private
  string-width@4.2.3:
    string-width: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@3.0.1:
    strip-ansi: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@4.0.0:
    strip-final-newline: private
  strip-json-comments@2.0.1:
    strip-json-comments: private
  strip-literal@3.0.0:
    strip-literal: private
  superjson@2.2.2:
    superjson: private
  supports-color@3.2.3:
    supports-color: private
  svg-baker@1.7.0:
    svg-baker: private
  svgo@2.8.0:
    svgo: private
  symbol-tree@3.2.4:
    symbol-tree: private
  tar@6.2.1:
    tar: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-object-path@0.3.0:
    to-object-path: private
  to-regex-range@2.1.1:
    to-regex-range: private
  to-regex-range@5.0.1:
    to-regex-range: private
  to-regex@3.0.2:
    to-regex: private
  totalist@3.0.1:
    totalist: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@3.0.0:
    tr46: private
  traverse@0.6.11:
    traverse: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray.prototype.slice@1.0.5:
    typedarray.prototype.slice: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unconfig@7.3.2:
    unconfig: private
  undici-types@6.21.0:
    undici-types: private
  undici-types@7.10.0:
    undici-types: private
  undici-types@7.8.0:
    undici-types: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  unimport@4.2.0:
    unimport: private
  union-value@1.0.1:
    union-value: private
  universalify@0.2.0:
    universalify: private
  universalify@2.0.1:
    universalify: private
  unplugin-utils@0.2.4:
    unplugin-utils: private
  unplugin@2.3.5:
    unplugin: private
  unset-value@1.0.0:
    unset-value: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  urix@0.1.0:
    urix: private
  url-parse@1.5.10:
    url-parse: private
  use@3.1.1:
    use: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vary@1.1.2:
    vary: private
  vite-hot-client@2.1.0(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1)):
    vite-hot-client: private
  vite-hot-client@2.1.0(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)):
    vite-hot-client: private
  vite-hot-client@2.1.0(vite@7.0.6(@types/node@22.16.5)):
    vite-hot-client: private
  vite-plugin-inspect@0.8.9(rollup@4.45.3)(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1)):
    vite-plugin-inspect: private
  vite-plugin-inspect@0.8.9(rollup@4.45.3)(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)):
    vite-plugin-inspect: private
  vite-plugin-inspect@0.8.9(rollup@4.45.3)(vite@7.0.6(@types/node@22.16.5)):
    vite-plugin-inspect: private
  vite-plugin-vue-inspector@5.3.2(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)(terser@5.43.1)):
    vite-plugin-vue-inspector: private
  vite-plugin-vue-inspector@5.3.2(vite@7.0.6(@types/node@22.16.5)(jiti@2.5.1)(sass@1.89.2)):
    vite-plugin-vue-inspector: private
  vite-plugin-vue-inspector@5.3.2(vite@7.0.6(@types/node@22.16.5)):
    vite-plugin-vue-inspector: private
  vscode-uri@3.1.0:
    vscode-uri: private
  vue-flow-layout@0.1.1(vue@3.5.18(typescript@5.8.3)):
    vue-flow-layout: private
  vue-flow-layout@0.2.0:
    vue-flow-layout: private
  w3c-xmlserializer@4.0.0:
    w3c-xmlserializer: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  weixin-js-sdk@1.6.5:
    weixin-js-sdk: private
  whatwg-encoding@2.0.0:
    whatwg-encoding: private
  whatwg-mimetype@3.0.0:
    whatwg-mimetype: private
  whatwg-url@11.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@5.0.0:
    which: private
  wide-align@1.1.5:
    wide-align: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3:
    ws: private
  wsl-utils@0.1.0:
    wsl-utils: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  yallist@3.1.1:
    yallist: private
  yallist@4.0.0:
    yallist: private
  yoctocolors@2.1.1:
    yoctocolors: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Mon, 04 Aug 2025 15:32:30 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.45.3'
  - '@rollup/rollup-android-arm-eabi@4.46.0'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.45.3'
  - '@rollup/rollup-android-arm64@4.46.0'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.45.3'
  - '@rollup/rollup-darwin-arm64@4.46.0'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.45.3'
  - '@rollup/rollup-darwin-x64@4.46.0'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.45.3'
  - '@rollup/rollup-freebsd-arm64@4.46.0'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.45.3'
  - '@rollup/rollup-freebsd-x64@4.46.0'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.3'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.3'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.45.3'
  - '@rollup/rollup-linux-arm64-gnu@4.46.0'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.45.3'
  - '@rollup/rollup-linux-arm64-musl@4.46.0'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.3'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.45.3'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.3'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.45.3'
  - '@rollup/rollup-linux-riscv64-musl@4.46.0'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.45.3'
  - '@rollup/rollup-linux-s390x-gnu@4.46.0'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.45.3'
  - '@rollup/rollup-linux-x64-gnu@4.46.0'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.45.3'
  - '@rollup/rollup-linux-x64-musl@4.46.0'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.45.3'
  - '@rollup/rollup-win32-arm64-msvc@4.46.0'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.45.3'
  - '@rollup/rollup-win32-ia32-msvc@4.46.0'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\projects\MyProjects\250726-无畏契约周年庆\h5\node_modules\.pnpm
virtualStoreDirMaxLength: 60
