<template>
  <div class="area5-view">

    <!-- 可滚动图片组件 -->
    <ScrollableImageView
      image-path="/src/assets/images/area/area5.jpg"
      alt="惊喜瓦礼"
      @image-load="onImageLoad"
      @image-error="onImageError"
    >
      <!-- 区域5特有的功能内容 -->
      <template #content="{ imageLoaded }">
        <div v-if="imageLoaded" class="area5-content">
          <!-- 这里可以添加区域5特有的交互功能 -->
          <!-- 例如：礼品领取、抽奖活动等 -->
          <div class="floating-actions">
            <!-- 示例：区域5特有功能按钮 -->
            <!-- <van-button type="danger" @click="handleArea5Action">
              领取礼品
            </van-button> -->
          </div>
        </div>
      </template>
    </ScrollableImageView>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import ScrollableImageView from '@/components/ScrollableImageView.vue'

const router = useRouter()

// 返回首页
const goBack = () => {
  router.push('/')
}

// 图片加载成功回调
const onImageLoad = (url: string) => {
  console.log('区域5图片加载成功:', url)
  // 这里可以添加区域5特有的初始化逻辑
}

// 图片加载失败回调
const onImageError = (error: string) => {
  console.error('区域5图片加载失败:', error)
}

// 区域5特有的功能方法
const handleArea5Action = () => {
  // 区域5特有的业务逻辑（惊喜瓦礼相关）
  console.log('执行区域5特有功能')
}
</script>

<style lang="scss" scoped>
.area5-view {
  position: relative;
  min-height: 100vh;
}

.area5-content {
  position: relative;
  z-index: 5;
}

.floating-actions {
  position: fixed;
  bottom: 40px;
  right: 20px;
  z-index: 10;

  // 可以添加区域5特有的样式（惊喜瓦礼主题）
}

// 区域5特有的样式
// 例如：礼品相关的动画效果
</style>