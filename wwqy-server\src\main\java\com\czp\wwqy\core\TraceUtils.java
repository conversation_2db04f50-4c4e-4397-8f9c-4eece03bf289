package com.czp.wwqy.core;

import cn.hutool.extra.spring.SpringUtil;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 *
 */
@Slf4j
@Component
public class TraceUtils {

    private final Tracer tracer;

    public TraceUtils(Tracer tracer) {
        this.tracer = tracer;
    }

    public static String getTraceId() {
        try {
            TraceUtils bean = SpringUtil.getBean(TraceUtils.class);
            return bean.traceId();
        } catch (Exception e) {
            log.error("获取traceId异常", e);
        }
        return null;
    }

    private String traceId() {
        Span span = tracer.currentSpan();
        if (span == null) {
            span = tracer.nextSpan();
        }
        return span.context().traceId();
    }


    public static String getSpanId() {
        try {
            TraceUtils bean = SpringUtil.getBean(TraceUtils.class);
            return bean.spanId();
        } catch (Exception e) {
            log.error("获取spanId异常", e);
        }
        return null;
    }

    private String spanId() {
        Span span = tracer.currentSpan();
        if (span == null) {
            span = tracer.nextSpan();
        }
        return span.context().spanId();
    }

}