# 用户头像和昵称显示功能

## 功能概述
在登录页面显示用户的微信头像和昵称，提升用户体验和界面友好性。

## 实现效果

### 未登录状态
- 显示"微信授权登录"按钮
- 显示功能说明文字

### 已登录状态
- 显示用户头像（圆形，80x80px）
- 显示用户昵称
- 显示"登录成功，欢迎参与活动！"提示
- 显示"进入主页"按钮

## 技术实现

### 1. 用户信息获取
用户信息通过微信OAuth2授权登录获取，存储在Pinia store中：

```typescript
// 从userStore获取用户信息
const userStore = useUserStore()

// 用户昵称：userStore.nickname
// 用户头像：userStore.avatarUrl
// 登录状态：userStore.isLoggedIn
```

### 2. 界面布局
使用响应式设计，在登录按钮上方显示用户信息卡片：

```vue
<!-- 用户信息显示区域 -->
<div v-if="userStore.isLoggedIn" class="w-full max-w-300 mb-40">
  <div class="bg-white/10 backdrop-blur-sm rounded-20 p-20 text-center">
    <!-- 用户头像 -->
    <div class="w-80 h-80 mx-auto mb-16 rounded-full overflow-hidden bg-white/20">
      <img 
        v-if="userStore.avatarUrl" 
        :src="userStore.avatarUrl" 
        :alt="userStore.nickname || '用户头像'"
        class="w-full h-full object-cover"
        @error="handleAvatarError"
      />
      <div v-else class="w-full h-full flex items-center justify-center">
        <van-icon name="user-o" size="32" color="rgba(255,255,255,0.6)" />
      </div>
    </div>
    
    <!-- 用户昵称 -->
    <div class="text-18 font-medium text-white mb-8">
      {{ userStore.nickname || '微信用户' }}
    </div>
    
    <!-- 登录状态 -->
    <div class="text-14 text-white/70">
      登录成功，欢迎参与活动！
    </div>
  </div>
</div>
```

### 3. 样式设计
- **背景效果**：使用`bg-white/10 backdrop-blur-sm`创建半透明毛玻璃效果
- **圆角设计**：使用`rounded-20`创建圆角卡片
- **头像样式**：圆形头像，带有背景色和溢出隐藏
- **文字层次**：昵称使用较大字体和高亮色，状态文字使用较小字体和半透明色

### 4. 错误处理
添加头像加载失败的处理机制：

```typescript
// 处理头像加载错误
const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  console.warn('头像加载失败:', img.src)
}
```

**处理逻辑**：
- 当头像图片加载失败时，隐藏图片元素
- 显示默认的用户图标
- 在控制台输出警告信息便于调试

### 5. 默认状态
当用户没有头像或头像加载失败时，显示默认图标：

```vue
<div v-else class="w-full h-full flex items-center justify-center">
  <van-icon name="user-o" size="32" color="rgba(255,255,255,0.6)" />
</div>
```

## 用户体验优化

### 1. 视觉层次
- 头像作为视觉焦点，居中显示
- 昵称使用较大字体突出显示
- 状态文字使用较小字体作为辅助信息

### 2. 交互反馈
- 登录按钮文字动态显示loading状态
- 头像加载失败时优雅降级到默认图标
- 整体布局保持一致性

### 3. 响应式设计
- 使用相对单位确保在不同设备上正常显示
- 最大宽度限制确保在大屏设备上不会过宽
- 间距和字体大小适配移动端

## 数据流程

### 1. 登录流程
```
用户点击登录 → 微信授权 → 获取用户信息 → 存储到userStore → 页面自动更新显示
```

### 2. 状态管理
```
userStore.userInfo → computed属性 → 响应式更新 → 界面重新渲染
```

### 3. 持久化
用户信息通过`pinia-plugin-persistedstate`自动持久化到localStorage，页面刷新后仍然保持登录状态。

## 相关文件
- `h5/src/views/LoginView.vue` - 主要实现文件
- `h5/src/stores/user.ts` - 用户状态管理
- `h5/src/api/user.ts` - 用户API接口

## 测试建议
1. 测试正常登录后头像和昵称显示
2. 测试头像加载失败时的默认图标显示
3. 测试用户昵称为空时的默认显示
4. 测试页面刷新后用户信息持久化
5. 测试不同设备尺寸下的显示效果

这个功能让用户在登录后能够清楚地看到自己的身份信息，增强了应用的个性化体验。
