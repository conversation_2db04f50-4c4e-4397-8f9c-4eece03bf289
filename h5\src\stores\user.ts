import { defineStore } from 'pinia'
import type { UserInfo } from '@/api/user'

// 用户登录状态接口
interface UserLoginState {
  userInfo: UserInfo
  loginTime: number
  expiresIn: number // 有效期（天数）
}

export const useUserStore = defineStore('user', {
  state: (): { loginState: UserLoginState | null } => ({
    loginState: null,
  }),

  getters: {
    userInfo: (state): UserInfo | null => state.loginState?.userInfo || null,

    isLoggedIn(state): boolean {
      if (!state.loginState) return false

      // 检查是否过期
      const now = Date.now()
      const loginTime = state.loginState.loginTime
      const expiresIn = state.loginState.expiresIn || 30 // 默认30天
      const expireTime = loginTime + (expiresIn * 24 * 60 * 60 * 1000)

      if (now > expireTime) {
        // 已过期，清除登录状态
        state.loginState = null
        return false
      }

      return !!state.loginState.userInfo?.openid
    },

    nickname(): string {
      return this.userInfo?.nickname || ''
    },

    avatarUrl(): string {
      return this.userInfo?.avatarUrl || ''
    },

    openid(): string {
      return this.userInfo?.openid || ''
    },

    // 剩余有效天数
    remainingDays(state): number {
      if (!state.loginState) return 0

      const now = Date.now()
      const loginTime = state.loginState.loginTime
      const expiresIn = state.loginState.expiresIn || 30
      const expireTime = loginTime + (expiresIn * 24 * 60 * 60 * 1000)
      const remainingMs = expireTime - now

      if (remainingMs <= 0) return 0

      return Math.ceil(remainingMs / (24 * 60 * 60 * 1000))
    },
  },

  actions: {
    setUserInfo(info: UserInfo, expiresInDays: number = 30) {
      const now = Date.now()
      this.loginState = {
        userInfo: info,
        loginTime: now,
        expiresIn: expiresInDays,
      }
    },

    updateUserInfo(updates: Partial<UserInfo>) {
      if (this.loginState) {
        const updated = { ...this.loginState.userInfo, ...updates }
        this.setUserInfo(updated)
      }
    },

    clearUserInfo() {
      this.loginState = null
    },
  },
  persist: {
    key: 'wx_user_store',
    storage: localStorage,
  }
})