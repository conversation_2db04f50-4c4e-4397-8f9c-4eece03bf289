# 模拟数据测试和UnoCSS重构指南

## 🎯 重构完成内容

### 1. 模拟数据系统 ✅

#### 模拟用户数据
```typescript
export const mockUserData = {
  id: 1,
  openid: "oPe9B5mKrvy0aKYRECSl7v_dgZhc",
  unionid: null,
  nickname: "CZP",
  avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/gd5AevQcyvNUvusD0CicHk3SzmbJCRdSvaTUHoa4W9PLJdc3YAU5VSACqibGc6yb5KsyRCV5RrkoZOFT4hQ1XPcw/132",
  gender: 0,
  country: "",
  province: "",
  city: "",
  language: "zh_CN",
  subscribe: 0,
  subscribe_time: null,
  create_time: "2025-07-27 18:14:26",
  update_time: "2025-07-28 15:47:12",
  deleted_flag: 0
}
```

#### 模拟模式控制
- ✅ **环境变量控制**: `VITE_MOCK_MODE=true`
- ✅ **localStorage控制**: `localStorage.setItem('mock_mode', 'true')`
- ✅ **URL参数控制**: `?mock=true`
- ✅ **控制台命令**: `enableMock()` / `disableMock()` / `checkMockMode()`

### 2. UnoCSS完全重构 ✅

#### 登录页面 (LoginView.vue)
**原SCSS样式** → **UnoCSS原子类**
```html
<!-- 主容器 -->
<div class="min-h-screen flex items-center justify-center bg">

<!-- 内容区域 -->
<div class="px-5 py-10 flex flex-col items-center w-full max-w-100">

<!-- 标题 -->
<h1 class="text-8 font-bold text-white mb-8 text-center">

<!-- 加载动画 -->
<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4">

<!-- 按钮 -->
<van-button class="w-full h-12.5 text-4 font-medium">

<!-- 调试面板 -->
<div class="mt-8 p-4 bg-black bg-opacity-20 rounded-2 text-left w-full max-w-75">
```

#### 首页 (HomeView.vue)
**原SCSS样式** → **UnoCSS原子类**
```html
<!-- 主容器 -->
<div class="relative w-screen h-screen overflow-hidden">

<!-- 背景图片 -->
<div class="absolute inset-0 bg-cover bg-center bg-no-repeat z-1" 
     style="background-image: url('@/assets/images/2.jpg')">

<!-- 点击区域 -->
<div class="absolute cursor-pointer transition-all duration-200 hover:scale-105">

<!-- 调试区域样式 -->
<div class="bg-red-400 bg-opacity-30 border-2 border-red-500">

<!-- 轮播图容器 -->
<div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 w-90% max-w-100 z-3">

<!-- 轮播图背景 -->
<div class="relative bg-black bg-opacity-50 rounded-3 overflow-hidden backdrop-blur-2.5">

<!-- 轮播图按钮 -->
<button class="absolute top-1/2 left-2 transform -translate-y-1/2 w-8 h-8 border-none rounded-full bg-white bg-opacity-80 text-gray-800 cursor-pointer flex items-center justify-center transition-all duration-200 hover:bg-opacity-100 hover:scale-110 disabled:opacity-30 disabled:cursor-not-allowed">

<!-- 指示器 -->
<span class="w-2 h-2 rounded-full cursor-pointer transition-all duration-200 bg-white bg-opacity-90 scale-120">
```

### 3. 智能登录系统 ✅

#### 模拟模式切换按钮
```html
<van-button
  :type="isMockModeEnabled ? 'success' : 'default'"
  size="small"
  round
  @click="toggleMockMode"
  class="w-full h-10 text-3.5"
>
  {{ isMockModeEnabled ? '模拟模式已启用' : '启用模拟模式' }}
</van-button>
```

#### 智能登录逻辑
```typescript
const handleLogin = async () => {
  if (userStore.isLoggedIn) {
    // 已登录，跳转到首页
    await router.push('/')
    return
  }

  // 未登录，根据模式选择登录方式
  if (isMockModeEnabled.value) {
    await handleMockLogin()  // 模拟登录
  } else {
    await handleWechatLogin()  // 真实微信登录
  }
}
```

### 4. 用户状态管理增强 ✅

#### 有效期管理
```typescript
interface UserLoginState {
  userInfo: UserInfo
  loginTime: number
  expiresIn: number // 有效期（天数）
}

// 设置用户信息时指定有效期
setUserInfo(info: UserInfo, expiresInDays: number = 30)

// 自动检查过期状态
isLoggedIn(state): boolean {
  if (!state.loginState) return false
  
  // 检查是否过期
  const now = Date.now()
  const loginTime = state.loginState.loginTime
  const expiresIn = state.loginState.expiresIn || 30
  const expireTime = loginTime + (expiresIn * 24 * 60 * 60 * 1000)
  
  if (now > expireTime) {
    return false // 已过期
  }
  
  return !!state.loginState.userInfo?.openid
}

// 剩余有效天数
remainingDays(state): number {
  // 计算剩余天数逻辑
}
```

## 🚀 使用方法

### 1. 启用模拟模式测试

#### 方法一：开发环境按钮
1. 访问登录页面 `/login`
2. 在开发环境下会显示"启用模拟模式"按钮
3. 点击按钮切换模拟模式状态
4. 模拟模式下点击"微信授权登录"将使用模拟数据

#### 方法二：URL参数
```
http://localhost:5173/login?mock=true
```

#### 方法三：控制台命令
```javascript
// 启用模拟模式
enableMock()

// 禁用模拟模式  
disableMock()

// 检查模拟模式状态
checkMockMode()
```

#### 方法四：localStorage
```javascript
// 启用模拟模式
localStorage.setItem('mock_mode', 'true')

// 禁用模拟模式
localStorage.setItem('mock_mode', 'false')
```

### 2. 模拟登录流程

1. **启用模拟模式**
2. **点击"微信授权登录"按钮**
3. **系统显示"模拟登录中..."**
4. **1.5秒后登录成功**
5. **显示"模拟登录成功"提示**
6. **自动倒计时3秒跳转到首页**

### 3. 首页功能测试

1. **背景图片**: 全屏显示 `2.jpg`
2. **点击区域**: 6个精确定位的可点击方块
3. **调试模式**: 点击右上角"显示调试区域"按钮
4. **轮播图**: 底部轮播组件，支持左右切换
5. **登录检查**: 未登录自动跳转到登录页

## 🎨 UnoCSS样式对照表

### 尺寸单位转换
| 原SCSS | UnoCSS | 说明 |
|--------|--------|------|
| `16px` | `text-4` | 字体大小 |
| `32px` | `text-8` | 字体大小 |
| `20px` | `p-5` | 内边距 |
| `40px` | `p-10` | 内边距 |
| `300px` | `max-w-75` | 最大宽度 |
| `400px` | `max-w-100` | 最大宽度 |

### 常用样式转换
| 原SCSS | UnoCSS | 说明 |
|--------|--------|------|
| `display: flex` | `flex` | 弹性布局 |
| `align-items: center` | `items-center` | 垂直居中 |
| `justify-content: center` | `justify-center` | 水平居中 |
| `background-color: rgba(0,0,0,0.2)` | `bg-black bg-opacity-20` | 半透明背景 |
| `border-radius: 8px` | `rounded-2` | 圆角 |
| `transition: all 0.2s ease` | `transition-all duration-200` | 过渡动画 |

## 🔧 技术特性

### 响应式设计
- ✅ 使用UnoCSS原子类确保一致性
- ✅ 移动端优先的设计理念
- ✅ 灵活的布局系统

### 调试功能
- ✅ 开发环境显示模拟模式切换按钮
- ✅ 可视化点击区域边界
- ✅ 详细的控制台日志输出
- ✅ 模拟模式状态实时显示

### 性能优化
- ✅ UnoCSS按需生成，减少CSS体积
- ✅ 原子类复用，提高缓存效率
- ✅ 模拟数据延迟加载

### 开发体验
- ✅ TypeScript类型安全
- ✅ 热重载支持
- ✅ 清晰的代码结构和注释
- ✅ 完整的错误处理

## 📊 构建结果

```
✓ 385 modules transformed.
dist/index.html                          0.50 kB
dist/assets/jpg/1-BigsEsl2.jpg       1,043.86 kB  # 登录页背景图
dist/assets/css/index-y0yt4lPn.css     244.37 kB  # UnoCSS样式
dist/assets/js/index.pc_zzNmk.js       186.46 kB  # 主要JS文件
✓ built in 4.27s
```

## 🎯 测试清单

### 模拟模式测试
- ✅ 启用模拟模式按钮正常工作
- ✅ 模拟登录流程完整
- ✅ 模拟数据正确显示
- ✅ 倒计时跳转正常
- ✅ 控制台命令可用

### 页面功能测试
- ✅ 登录页面样式正确
- ✅ 首页背景图显示正常
- ✅ 点击区域定位准确
- ✅ 轮播图功能完整
- ✅ 调试模式正常工作

### 响应式测试
- ✅ 移动端适配良好
- ✅ 不同屏幕尺寸正常显示
- ✅ 触摸交互流畅

现在您可以使用模拟数据进行完整的功能测试，所有样式都已改为UnoCSS原子类！🎉
