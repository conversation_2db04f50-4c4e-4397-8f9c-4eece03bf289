package com.czp.wwqy.bus.service;

import com.czp.wwqy.bus.dto.*;
import com.czp.wwqy.bus.entity.WxUser;
import com.czp.wwqy.common.AppException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 微信服务类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatService {

    private final WxMpService wxMpService;
    private final WxUserService wxUserService;

    /**
     * 获取微信授权URL
     *
     * @param request 请求参数
     * @return 授权URL响应
     */
    public WechatAuthUrlResponse getAuthUrl(WechatAuthUrlRequest request) {
        try {
            String authUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(request.getRedirectUri(), "snsapi_userinfo", request.getState());

            log.info("生成微信授权URL成功: {}", authUrl);
            return new WechatAuthUrlResponse(authUrl);
        } catch (Exception e) {
            log.error("生成微信授权URL失败", e);
            throw AppException.of("生成授权URL失败");
        }
    }

    /**
     * 微信授权登录
     *
     * @param request 登录请求参数
     * @return 登录响应
     */
    public WechatLoginResponse login(WechatLoginRequest request) {
        try {
            // 通过授权码获取用户信息
            WxUser wxUser = getUserInfoByCode(request.getCode());

            WxUser savedUser = wxUserService.getByOpenid(wxUser.getOpenid());
            if (savedUser != null) {
                wxUser.setId(savedUser.getId());
                wxUserService.updateById(wxUser);
                log.info("用户已存在，更新用户信息：{}", wxUser);
            } else {
                wxUserService.save(wxUser);
                log.info("用户不存在，保存用户信息：{}", wxUser);
            }
            log.info("微信登录成功，OpenID: {}", wxUser.getOpenid());
            return new WechatLoginResponse(wxUser);

        } catch (Exception e) {
            log.error("微信登录失败", e);
            throw AppException.of("微信登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信二维码ticket
     *
     * @param request 请求参数
     * @return 二维码响应
     */
    public QrCodeTicketResponse getQrCodeTicket(QrCodeTicketRequest request) {
        try {
            WxMpQrCodeTicket ticket;

            // 临时二维码
            int expireSeconds = request.getExpireSeconds() != null ? request.getExpireSeconds() : 2592000; // 默认30天
            ticket = wxMpService.getQrcodeService().qrCodeCreateTmpTicket(request.getSceneStr(), expireSeconds);

            String qrCodeUrl = wxMpService.getQrcodeService().qrCodePictureUrl(ticket.getTicket());

            log.info("获取微信二维码ticket成功: {}", ticket.getTicket());
            return new QrCodeTicketResponse(ticket.getTicket(), qrCodeUrl, ticket.getExpireSeconds());

        } catch (WxErrorException e) {
            log.error("获取微信二维码ticket失败", e);
            throw AppException.of("获取二维码失败: " + e.getError().getErrorMsg());
        }
    }

    /**
     * 通过授权码获取用户信息
     *
     * @param code 授权码
     * @return 用户信息
     */
    public WxUser getUserInfoByCode(String code) {
        try {
            // 通过授权码获取access_token
            WxOAuth2AccessToken accessToken = wxMpService.getOAuth2Service().getAccessToken(code);

            // 通过access_token获取用户信息
            WxOAuth2UserInfo wxUser = wxMpService.getOAuth2Service().getUserInfo(accessToken, "zh_CN");

            WxUser user = new WxUser();
            user.setOpenid(wxUser.getOpenid());
            user.setUnionid(wxUser.getUnionId());
            user.setNickname(wxUser.getNickname());
            user.setAvatarUrl(wxUser.getHeadImgUrl());
            user.setGender(wxUser.getSex());
            user.setCountry(wxUser.getCountry());
            user.setProvince(wxUser.getProvince());
            user.setCity(wxUser.getCity());
            user.setLanguage("zh_CN"); // 默认设置为中文

            log.info("获取微信用户信息成功，OpenID: {}", user.getOpenid());
            return user;

        } catch (WxErrorException e) {
            log.error("获取微信用户信息失败", e);
            throw AppException.of("获取用户信息失败: " + e.getError().getErrorMsg());
        }
    }

    /**
     * 获取微信JS-SDK配置
     *
     * @param request 请求参数
     * @return JS-SDK配置响应
     */
    public WechatJsConfigResponse getJsConfig(WechatJsConfigRequest request) {
        try {
            // 获取JS-SDK签名
            WxJsapiSignature jsapiSignature = wxMpService.createJsapiSignature(request.getUrl());

            // 定义需要使用的JS接口列表
            List<String> jsApiList = Arrays.asList(
                    "updateAppMessageShareData", // 分享给朋友
                    "updateTimelineShareData",   // 分享到朋友圈
                    "scanQRCode"                 // 扫一扫
            );

            log.info("获取微信JS-SDK配置成功，URL: {}", request.getUrl());
            return new WechatJsConfigResponse(
                    jsapiSignature.getAppId(),
                    jsapiSignature.getTimestamp(),
                    jsapiSignature.getNonceStr(),
                    jsapiSignature.getSignature(),
                    jsApiList
            );

        } catch (WxErrorException e) {
            log.error("获取微信JS-SDK配置失败", e);
            throw AppException.of("获取JS-SDK配置失败: " + e.getError().getErrorMsg());
        }
    }
}