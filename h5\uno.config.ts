import { defineConfig, presetAttributify, presetIcons, presetWind3 } from 'unocss'
import presetRemToPx from '@unocss/preset-rem-to-px'

export default defineConfig({
  presets: [
    presetWind3(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
    presetRemToPx({
      baseFontSize: 4,
    }),
  ],
  shortcuts: [
    // 移动端常用样式
    ['flex-center', 'flex items-center justify-center'],
    ['flex-between', 'flex items-center justify-between'],
    ['flex-around', 'flex items-center justify-around'],
    ['flex-col-center', 'flex flex-col items-center justify-center'],
    ['absolute-center', 'absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'],
    ['fixed-center', 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'],

    // 文本样式
    ['text-ellipsis', 'overflow-hidden whitespace-nowrap text-ellipsis'],
    ['text-ellipsis-2', 'overflow-hidden line-clamp-2'],
    ['text-ellipsis-3', 'overflow-hidden line-clamp-3'],

    // 按钮样式
    ['btn-primary', 'bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 active:bg-blue-700'],
    ['btn-secondary', 'bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 active:bg-gray-400'],
  ],
  theme: {
    colors: {
      primary: '#1989fa',
      success: '#07c160',
      warning: '#ff976a',
      danger: '#ee0a24',
    },
    breakpoints: {
      xs: '320px',
      sm: '375px',
      md: '414px',
      lg: '768px',
      xl: '1024px',
    },
  },
  rules: [
    // 移动端安全区域
    ['safe-area-top', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['safe-area-bottom', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
    ['safe-area-left', { 'padding-left': 'env(safe-area-inset-left)' }],
    ['safe-area-right', { 'padding-right': 'env(safe-area-inset-right)' }],
  ],
})