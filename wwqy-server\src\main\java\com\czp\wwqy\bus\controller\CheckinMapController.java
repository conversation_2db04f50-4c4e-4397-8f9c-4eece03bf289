package com.czp.wwqy.bus.controller;

import com.czp.wwqy.bus.dto.CheckinListRequest;
import com.czp.wwqy.bus.dto.CheckinRequest;
import com.czp.wwqy.bus.dto.CheckinResponse;
import com.czp.wwqy.bus.service.CheckinMapService;
import com.czp.wwqy.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 打卡地图控制器
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/checkin")
@RequiredArgsConstructor
public class CheckinMapController {

    private final CheckinMapService checkinMapService;

    /**
     * 用户打卡
     *
     * @param request 打卡请求
     * @return 打卡结果
     */
    @PostMapping("/checkin")
    public Result<CheckinResponse> checkin(@Validated @RequestBody CheckinRequest request) {
        log.info("用户打卡，OpenID: {}, 地点: {}", request.getOpenid(), request.getLocationName());
        
        CheckinResponse response = checkinMapService.checkin(request);
        return Result.ok(response);
    }

    /**
     * 获取用户打卡记录列表
     *
     * @param request 请求参数
     * @return 打卡记录列表
     */
    @PostMapping("/list")
    public Result<List<CheckinResponse>> getCheckinList(@Validated @RequestBody CheckinListRequest request) {
        log.info("获取用户打卡记录列表，OpenID: {}", request.getOpenid());
        
        List<CheckinResponse> checkinList = checkinMapService.getCheckinList(request);
        return Result.ok(checkinList);
    }

    /**
     * 根据OpenID获取用户打卡记录
     *
     * @param openid 用户OpenID
     * @return 打卡记录列表
     */
    @GetMapping("/list/{openid}")
    public Result<List<CheckinResponse>> getCheckinListByOpenid(@PathVariable String openid) {
        log.info("根据OpenID获取用户打卡记录，OpenID: {}", openid);
        
        CheckinListRequest request = new CheckinListRequest();
        request.setOpenid(openid);
        
        List<CheckinResponse> checkinList = checkinMapService.getCheckinList(request);
        return Result.ok(checkinList);
    }

    /**
     * 测试接口
     *
     * @return 测试结果
     */
    @GetMapping("/test")
    public Result<String> test() {
        return Result.ok("打卡地图服务正常运行");
    }
}