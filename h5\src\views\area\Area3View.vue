<template>
  <div class="area3-view">

    <!-- 可滚动图片组件 -->
    <ScrollableImageView
      :image-url="area3Image"
      alt="制爆街区"
      @image-load="onImageLoad"
      @image-error="onImageError"
    >
      <!-- 区域3特有的功能内容 -->
      <template #content="{ imageLoaded }">
        <div v-if="imageLoaded" class="area3-content">
          <!-- 这里可以添加区域3特有的交互功能 -->
          <!-- 例如：制爆相关的互动元素 -->
          <div class="floating-actions">
            <!-- 示例：区域3特有功能按钮 -->
            <!-- <van-button type="warning" @click="handleArea3Action">
              制爆挑战
            </van-button> -->
          </div>
        </div>
      </template>
    </ScrollableImageView>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import ScrollableImageView from '@/components/ScrollableImageView.vue'
import area3Image from '@/assets/images/area/area3.jpg'

const router = useRouter()

// 返回首页
const goBack = () => {
  router.push('/')
}

// 图片加载成功回调
const onImageLoad = (url: string) => {
  console.log('区域3图片加载成功:', url)
  // 这里可以添加区域3特有的初始化逻辑
}

// 图片加载失败回调
const onImageError = (error: string) => {
  console.error('区域3图片加载失败:', error)
}

// 区域3特有的功能方法
const handleArea3Action = () => {
  // 区域3特有的业务逻辑（制爆街区相关）
  console.log('执行区域3特有功能')
}
</script>

<style lang="scss" scoped>
.area3-view {
  position: relative;
  min-height: 100vh;
}

.area3-content {
  position: relative;
  z-index: 5;
}

.floating-actions {
  position: fixed;
  bottom: 40px;
  right: 20px;
  z-index: 10;

  // 可以添加区域3特有的样式（制爆街区主题）
}

// 区域3特有的样式
// 例如：制爆相关的特效动画
</style>