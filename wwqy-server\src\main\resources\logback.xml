<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">

    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <property name="LOG_DIR" value="logs"/>
    <property name="PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %X{appKey:- } %X{traceId:- } [%15.15t] %-50.50(%logger{36}.%method#%line): %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <property name="MAX_HISTORY" value="30"/>
    <property name="MAX_FILE_SIZE" value="1GB"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
    </appender>

    <appender name="async_stdout" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="stdout"/>
    </appender>

    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/info/info.log</file>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/info/info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
        </rollingPolicy>
    </appender>

    <appender name="async_info" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="info"/>
    </appender>

    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/error/error.log</file>
        <encoder>
            <pattern>${PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/error/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
        </rollingPolicy>
    </appender>

    <appender name="async_error" class="ch.qos.logback.classic.AsyncAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="error"/>
    </appender>

    <logger name="tracer" level="TRACE" additivity="false">
        <appender-ref ref="async_info"/>
        <appender-ref ref="async_error"/>
        <appender-ref ref="async_stdout"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="async_info"/>
        <appender-ref ref="async_error"/>
        <appender-ref ref="async_stdout"/>
    </root>

</configuration>