import { post, get } from '@/utils/request'

// 打卡记录接口
export interface CheckinRecord {
  id: number
  userId: number
  locationName: string
  latitude: number
  longitude: number
  checkinTime: string
  createTime: string
}

// 打卡请求
export interface CheckinRequest {
  openid: string
  locationName: string
  latitude: number
  longitude: number
}

// 打卡响应
export interface CheckinResponse {
  id: number
  userId: number
  locationName: string
  latitude: number
  longitude: number
  checkinTime: string
  createTime: string
}

// 获取打卡记录列表请求
export interface GetCheckinListRequest {
  openid: string
  locationName?: string
}

// 获取打卡记录列表响应
export interface GetCheckinListResponse {
  checkinList: CheckinRecord[]
}

/**
 * 用户打卡
 */
export const CheckinApi = (data: CheckinRequest) => {
  return post<CheckinResponse>('/api/checkin/checkin', data, {
    showLoading: true
  })
}

/**
 * 获取用户打卡记录列表
 */
export const GetCheckinListApi = (data: GetCheckinListRequest) => {
  return post<CheckinRecord[]>('/api/checkin/list', data)
}

/**
 * 根据OpenID获取用户打卡记录
 */
export const GetCheckinListByOpenidApi = (openid: string) => {
  return get<CheckinRecord[]>(`/api/checkin/list/${openid}`)
}

/**
 * 测试打卡服务
 */
export const TestCheckinServiceApi = () => {
  return get<string>('/api/checkin/test')
}
