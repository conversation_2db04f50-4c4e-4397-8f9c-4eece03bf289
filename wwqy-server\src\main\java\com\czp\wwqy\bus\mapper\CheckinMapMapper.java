package com.czp.wwqy.bus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.czp.wwqy.bus.entity.CheckinMap;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 打卡地图Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Mapper
public interface CheckinMapMapper extends BaseMapper<CheckinMap> {

    /**
     * 根据用户ID查询打卡记录
     *
     * @param userId 用户ID
     * @return 打卡记录列表
     */
    List<CheckinMap> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和地点名称查询打卡记录
     *
     * @param userId 用户ID
     * @param locationName 地点名称
     * @return 打卡记录列表
     */
    List<CheckinMap> selectByUserIdAndLocation(@Param("userId") Long userId, @Param("locationName") String locationName);
}