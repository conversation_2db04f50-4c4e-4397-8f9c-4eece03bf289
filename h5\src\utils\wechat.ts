import wx from 'weixin-js-sdk'
import { GetWechatJsConfigApi } from '@/api/user'
import storage from '@/utils/storage.ts'

// 微信分享配置
export interface WechatShareConfig {
  title: string
  desc: string
  link: string
  imgUrl: string
}

/**
 * 微信工具类
 */
class WechatUtils {
  private static isJSSDKReady = false
  private static initPromise: Promise<void> | null = null

  /**
   * 检查是否在微信环境
   */
  static isWechatBrowser(): boolean {
    return /micromessenger/i.test(navigator.userAgent)
  }

  /**
   * 重置JS-SDK状态（用于错误恢复）
   */
  static reset(): void {
    WechatUtils.isJSSDKReady = false
    WechatUtils.initPromise = null
    console.log('微信JS-SDK状态已重置')
  }

  /**
   * 扫码功能
   */
  static async scanQRCode(): Promise<string> {
    // 强制重新初始化JSSDK以确保签名正确
    WechatUtils.reset()
    await WechatUtils.initJSSDK()

    return new Promise((resolve, reject) => {
      wx.ready(() => {
        wx.scanQRCode({
          needResult: 1,
          scanType: ['qrCode', 'barCode'],
          success: (res: any) => resolve(res.resultStr),
          fail: (res: any) => reject(new Error(`扫码失败: ${res.errMsg || '未知错误'}`)),
          cancel: (res: any) => reject(new Error(`用户取消扫码`)),
        })
      })

      wx.error((res: any) => {
        reject(new Error(`微信JS-SDK错误: ${res.errMsg}`))
      })
    })
  }

  /**
   * 配置微信分享
   */
  static async configShare(config: WechatShareConfig): Promise<void> {
    await WechatUtils.initJSSDK()

    return new Promise<void>((resolve, reject) => {
      wx.ready(() => {
        let completed = 0
        const total = 2

        const checkComplete = () => {
          if (++completed === total) resolve()
        }

        // 朋友圈分享
        wx.updateTimelineShareData({
          title: config.title,
          link: config.link,
          imgUrl: config.imgUrl,
          success: checkComplete,
          fail: (res: any) => reject(new Error(`朋友圈分享配置失败: ${res.errMsg}`)),
          cancel: (res: any) => reject(new Error(`朋友分享配置失败: ${res.errMsg}`)),
        })

        // 朋友分享
        wx.updateAppMessageShareData({
          title: config.title,
          desc: config.desc,
          link: config.link,
          imgUrl: config.imgUrl,
          success: checkComplete,
          fail: (res: any) => reject(new Error(`朋友分享配置失败: ${res.errMsg}`)),
          cancel: (res: any) => reject(new Error(`用户取消朋友分享`)),
        })
      })
    })
  }

  /**
   * 初始化微信JS-SDK
   */
  private static async initJSSDK(): Promise<void> {
    if (!WechatUtils.isWechatBrowser()) {
      throw new Error('请在微信浏览器中使用')
    }

    if (WechatUtils.isJSSDKReady) return

    if (WechatUtils.initPromise) return WechatUtils.initPromise

    WechatUtils.initPromise = WechatUtils.performInit()

    try {
      await WechatUtils.initPromise
      WechatUtils.isJSSDKReady = true
      console.log('微信JS-SDK初始化成功')
    } catch (error) {
      WechatUtils.initPromise = null
      throw error
    }
  }

  /**
   * 执行初始化
   */
  private static async performInit(): Promise<void> {
    // 获取当前URL，移除hash部分和微信授权参数
    let url = window.location.href.split('#')[0]

    // 移除微信授权相关参数，避免签名问题
    const urlObj = new URL(url)
    urlObj.searchParams.delete('code')
    urlObj.searchParams.delete('state')
    url = urlObj.toString()

    console.log('JSSDK初始化使用的URL:', url)

    const response = await GetWechatJsConfigApi({ url })

    if (!response.data) {
      throw new Error('获取微信配置失败')
    }

    return new Promise((resolve, reject) => {
      wx.config({
        appId: response.data.appId,
        timestamp: response.data.timestamp,
        nonceStr: response.data.nonceStr,
        signature: response.data.signature,
        jsApiList: response.data.jsApiList as wx.jsApiList,
        debug: false,
      })

      wx.ready(() => {
        console.log('微信JS-SDK配置成功')
        resolve()
      })
      wx.error((err: any) => {
        console.error('微信JS-SDK配置失败:', err)
        reject(new Error(`JS-SDK配置失败: ${err.errMsg}`))
      })
    })
  }
}

/**
 * 微信授权相关工具
 */
class WechatAuth {
  /**
   * 获取URL参数
   */
  static getUrlParam(name: string): string | null {
    return new URLSearchParams(window.location.search).get(name)
  }

  /**
   * 获取微信授权code
   */
  static getCode(): string | null {
    return WechatAuth.getUrlParam('code')
  }

  /**
   * 获取state参数
   */
  static getState(): string | null {
    return WechatAuth.getUrlParam('state')
  }

  /**
   * 生成随机字符串
   */
  static generateRandomString(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('')
  }



  /**
   * 跳转到微信授权页面
   */
  static redirectToAuth(authUrl: string): void {
    const currentPath = window.location.pathname + window.location.search
    storage.local.set('wechat_redirect_path', currentPath)
    window.location.href = authUrl
  }

  /**
   * 获取授权后的重定向路径
   */
  static getRedirectPath(): string {
    const savedPath = storage.local.get('wechat_redirect_path')
    if (savedPath) {
      storage.local.remove('wechat_redirect_path')
      return savedPath as string
    }
    return '/'
  }

  /**
   * 清理URL中的微信授权参数
   * 在用户登录成功后调用，避免影响JSSDK签名
   */
  static cleanAuthParams(): void {
    const url = new URL(window.location.href)
    let hasAuthParams = false

    if (url.searchParams.has('code')) {
      url.searchParams.delete('code')
      hasAuthParams = true
    }

    if (url.searchParams.has('state')) {
      url.searchParams.delete('state')
      hasAuthParams = true
    }

    if (hasAuthParams) {
      // 使用 replaceState 避免在浏览器历史中留下授权URL
      window.history.replaceState({}, document.title, url.toString())
      console.log('已清理URL中的微信授权参数')

      // 清理授权参数后，重置JSSDK状态以便重新初始化
      WechatUtils.reset()
    }
  }
}

// ==================== 导出的公共API ====================

// 环境检测
export const isWechat = WechatUtils.isWechatBrowser

// JS-SDK功能
export const scanQRCode = WechatUtils.scanQRCode
export const configWechatShare = WechatUtils.configShare
export const resetWechatSDK = WechatUtils.reset

// 授权相关
export const getWechatCode = WechatAuth.getCode
export const getWechatState = WechatAuth.getState
export const generateRandomString = WechatAuth.generateRandomString
export const redirectToWechatAuth = WechatAuth.redirectToAuth
export const getRedirectPath = WechatAuth.getRedirectPath
export const cleanWechatParams = WechatAuth.cleanAuthParams