<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { RouterView } from 'vue-router'

const touchmoveFunction = (event: TouchEvent) => {
  let node = event.target as HTMLElement
  while (node) {
    if (node.classList && node.classList.contains('scrollable')) {
      return // 如果事件目标是滚动容器或其子元素，则允许滚动
    }
    node = node.parentNode as HTMLElement // 向上检查父元素
  }
  event.preventDefault()
}

onMounted(() => {
  /* 阻止微信背景滚动 */
  document.addEventListener('touchmove', touchmoveFunction, { passive: false })
})

onUnmounted(() => {
  /* 移除touchmove事件 */
  document.removeEventListener('touchmove', touchmoveFunction)
})
</script>

<template>
  <div id="app" class="w-screen h-screen">
    <RouterView />
  </div>
</template>