<template>
  <div class="area2-view">
    <!-- 可滚动图片组件 -->
    <ScrollableImageView
      :image-url="area2Image"
      alt="打瓦空间"
    >
      <!-- 区域2特有的功能内容 -->
      <template #content="{ imageLoaded }">
        <div v-if="imageLoaded" class="area2-content">
          <!-- 这里可以添加区域2特有的交互功能 -->
          <!-- 例如：游戏控制、积分显示等 -->
          <div class="floating-actions">
            <!-- 示例：区域2特有功能按钮 -->
            <!-- <van-button type="success" @click="handleArea2Action">
              打瓦游戏
            </van-button> -->
          </div>
        </div>
      </template>
    </ScrollableImageView>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import ScrollableImageView from '@/components/ScrollableImageView.vue'
import area2Image from '@/assets/images/area/area2.jpg'


// 区域2特有的功能方法
const handleArea2Action = () => {
  // 区域2特有的业务逻辑（打瓦空间相关）
  console.log('执行区域2特有功能')
}
</script>

<style lang="scss" scoped>
.area2-view {
  position: relative;
  min-height: 100vh;
}

.area2-content {
  position: relative;
  z-index: 5;
}

.floating-actions {
  position: fixed;
  bottom: 40px;
  right: 20px;
  z-index: 10;

  // 可以添加区域2特有的样式（打瓦空间主题）
}

</style>