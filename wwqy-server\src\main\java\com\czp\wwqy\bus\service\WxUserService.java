package com.czp.wwqy.bus.service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.czp.wwqy.bus.entity.WxUser;
import com.czp.wwqy.bus.mapper.WxUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户服务类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@Service
public class WxUserService extends ServiceImpl<WxUserMapper, WxUser> {

    /**
     * 根据OpenID获取用户信息
     *
     * @param openid 微信OpenID
     * @return 用户信息
     */
    public WxUser getByOpenid(String openid) {
        LambdaQueryChainWrapper<WxUser> lambdaQuery = lambdaQuery().eq(WxUser::getOpenid, openid);
        return lambdaQuery.one();
    }

}