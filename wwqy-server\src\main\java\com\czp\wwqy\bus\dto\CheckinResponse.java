package com.czp.wwqy.bus.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 打卡响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckinResponse {

    /**
     * 打卡记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 地点名称
     */
    private String locationName;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 打卡时间
     */
    private LocalDateTime checkinTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}