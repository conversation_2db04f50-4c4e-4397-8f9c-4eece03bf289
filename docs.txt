请帮我创建一个无畏契约周年庆项目，包含前端H5页面和后端API服务。

**项目结构要求：**
- 前端项目位于 `h5/` 文件夹
- 后端项目位于 `server/` 文件夹
- 前后端完全分离架构

**前端技术栈：**
- 使用 Vite 创建 Vue 3 项目,不要eslint。使用pnpm.
- UI框架：Vant 4（移动端组件库）
- 样式：UnoCSS + SCSS
- 使用unplugin-vue-components和unplugin-auto-import实现按需加载
- 目标环境：微信浏览器移动端

**后端技术栈：**
- Spring Boot 3
- MyBatis-Plus（数据库ORM）
- MySQL 数据库
- WxJava（微信开发工具包）

**后端开发规范：**
- 统一返回体格式（包含code、message、data字段）
- 全局异常处理机制
- 所有API接口使用POST请求
- 请求DTO类名以Request结尾
- 响应DTO类名以Response结尾
- 配置文件使用application.yml格式
- 大量使用枚举类型和Lombok注解

**微信配置信息：**
- AppID: wxdac140cf05115764
- AppSecret: d95abff85d77cb791d8daecfae8d610f

**数据库设计要求：**
1. 用户表：存储微信用户信息（姓名、头像、openid等）
2. 打卡地图表：包含用户openid、地图id、地图名称、打卡时间、打卡状态等

**第一阶段实现目标（当前任务）：**
1. 搭建前端项目基础架构（Vue3 + Vite + Vant4）
2. 搭建后端项目基础架构（Spring Boot3 + MyBatis-Plus）
3. 实现微信OAuth2授权登录流程
4. 实现用户信息获取和数据库存储
5. 前端localStorage用户信息缓存机制
6. 创建简单的首页界面（临时设计，等待设计稿）
7. 实现微信扫码功能的ticket获取接口

**具体实现步骤：**
1. 初始化前端项目结构和依赖
2. 初始化后端项目结构和依赖
3. 配置数据库连接和表结构
4. 实现微信授权登录API
5. 实现用户信息保存API
6. 前端实现授权登录页面和首页
7. 前后端联调测试

请按照以上要求，先完成项目初始化和微信授权登录功能的实现。