<template>
  <div class="area1-view">
    <ScrollableImageView
      image-path="/src/assets/images/area/area1.jpg"
      alt="竞技空间"
    >
    </ScrollableImageView>
  </div>
</template>

<script setup lang="ts">
import ScrollableImageView from '@/components/ScrollableImageView.vue'


// 区域1特有的功能方法
const handleArea1Action = () => {
  // 区域1特有的业务逻辑
  console.log('执行区域1特有功能')
}
</script>

<style lang="scss" scoped>
.area1-view {
  min-height: 100vh;
  background-color: #000;
}

</style>