2025-08-05 14:06:02.938  INFO   [ionShutdownHook] .w.e.tomcat.GracefulShutdown.shutDownGracefully#54: Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 14:06:06.113  INFO   [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown.doShutdown#76   : Graceful shutdown complete
2025-08-05 20:58:31.774  INFO   [           main] com.czp.wwqy.WwqyServerApplication.logStarting#53 : Starting WwqyServerApplication using Java 17.0.16 with PID 15560 (D:\projects\MyProjects\250726-无畏契约周年庆\wwqy-server\target\classes started by zhipe in D:\projects\MyProjects\250726-无畏契约周年庆\wwqy-server)
2025-08-05 20:58:31.777  INFO   [           main] qy.WwqyServerApplication.logStartupProfileInfo#652: No active profile set, falling back to 1 default profile: "default"
2025-08-05 20:58:34.198  INFO   [           main] o.s.b.w.e.tomcat.TomcatWebServer.initialize#111   : <PERSON><PERSON> initialized with port 8080 (http)
2025-08-05 20:58:34.217  INFO   [           main] o.a.catalina.core.StandardService.log#168         : Starting service [Tomcat]
2025-08-05 20:58:34.217  INFO   [           main] o.a.catalina.core.StandardEngine.log#168          : Starting Servlet engine: [Apache Tomcat/10.1.43]
2025-08-05 20:58:34.290  INFO   [           main] o.a.c.c.C.[Tomcat].[localhost].[/].log#168        : Initializing Spring embedded WebApplicationContext
2025-08-05 20:58:34.291  INFO   [           main] pplicationContext.prepareWebApplicationContext#301: Root WebApplicationContext: initialization completed in 2439 ms
2025-08-05 20:58:37.364  INFO   [           main] usApplicationContextAware.setApplicationContext#40: Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6ce1f601
2025-08-05 20:58:37.923  INFO   [           main] o.s.b.a.e.web.EndpointLinksResolver.<init>#60     : Exposing 1 endpoint beneath base path '/actuator'
2025-08-05 20:58:38.040  INFO   [           main] o.s.b.w.e.tomcat.TomcatWebServer.start#243        : Tomcat started on port 8080 (http) with context path '/'
2025-08-05 20:58:41.895  INFO   [           main] com.czp.wwqy.WwqyServerApplication.logStarted#59  : Started WwqyServerApplication in 13.674 seconds (process running for 19.909)
2025-08-05 20:58:41.921  INFO   [           main] c.c.wwqy.core.ApplicationInfoRunner.run#49        : 
=============================================
huawei-server 启动成功
当前环境: default
启动端口: 8080
应用版本: unknown
=============================================

2025-08-05 21:10:32.881  INFO   [nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/].log#168        : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 21:10:32.881  INFO   [nio-8080-exec-1] .web.servlet.DispatcherServlet.initServletBean#532: Initializing Servlet 'dispatcherServlet'
2025-08-05 21:10:32.883  INFO   [nio-8080-exec-1] .web.servlet.DispatcherServlet.initServletBean#554: Completed initialization in 2 ms
2025-08-05 21:10:33.010 TRACE  689202c87e026409a154fe206c8957c2 [nio-8080-exec-1] org.zalando.logbook.Logbook.write#25              : {"body":{"redirectUri":"http://t44af468.natappfree.cc/login","state":"rQTXRqLDOtPxLsS0"},"correlation":"d02e0e3b5ed5db11","headers":{"content-type":["application/json"],"user-agent":["Mozilla/5.0 (iPhone; CPU iPhone OS 19_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d34) NetType/WIFI Language/zh_CN"],"x-real-ip":["127.0.0.1"]},"host":"t44af468.natappfree.cc","method":"POST","origin":"remote","path":"/wechat/auth-url","port":"80","protocol":"HTTP/1.0","remote":"0:0:0:0:0:0:0:1","scheme":"http","type":"request","uri":"http://t44af468.natappfree.cc/wechat/auth-url"}
2025-08-05 21:10:33.168  INFO  689202c87e026409a154fe206c8957c2 [nio-8080-exec-1] c.czp.wwqy.bus.service.WechatService.getAuthUrl#43: 生成微信授权URL成功: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=http%3A%2F%2Ft44af468.natappfree.cc%2Flogin&response_type=code&scope=snsapi_userinfo&state=rQTXRqLDOtPxLsS0&connect_redirect=1#wechat_redirect
2025-08-05 21:10:33.197 TRACE  689202c87e026409a154fe206c8957c2 [nio-8080-exec-1] org.zalando.logbook.Logbook.write#30              : {"body":{"code":200,"message":"操作成功","data":{"authUrl":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=http%3A%2F%2Ft44af468.natappfree.cc%2Flogin&response_type=code&scope=snsapi_userinfo&state=rQTXRqLDOtPxLsS0&connect_redirect=1#wechat_redirect"},"error":false,"success":true},"correlation":"d02e0e3b5ed5db11","duration":"267","headers":{"Content-Type":["application/json"]},"origin":"local","protocol":"HTTP/1.0","status":200,"type":"response"}
2025-08-05 21:11:11.128 TRACE  689202efefad324f2396609557ee4c75 [nio-8080-exec-3] org.zalando.logbook.Logbook.write#25              : {"body":{"redirectUri":"http://t44af468.natappfree.cc/login","state":"3P36UjFXZdgSXRzE"},"correlation":"c036a26f774fa1c6","headers":{"content-type":["application/json"],"user-agent":["Mozilla/5.0 (iPhone; CPU iPhone OS 19_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d34) NetType/WIFI Language/zh_CN"],"x-real-ip":["127.0.0.1"]},"host":"t44af468.natappfree.cc","method":"POST","origin":"remote","path":"/wechat/auth-url","port":"80","protocol":"HTTP/1.0","remote":"127.0.0.1","scheme":"http","type":"request","uri":"http://t44af468.natappfree.cc/wechat/auth-url"}
2025-08-05 21:11:11.130  INFO  689202efefad324f2396609557ee4c75 [nio-8080-exec-3] c.czp.wwqy.bus.service.WechatService.getAuthUrl#43: 生成微信授权URL成功: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=http%3A%2F%2Ft44af468.natappfree.cc%2Flogin&response_type=code&scope=snsapi_userinfo&state=3P36UjFXZdgSXRzE&connect_redirect=1#wechat_redirect
2025-08-05 21:11:11.132 TRACE  689202efefad324f2396609557ee4c75 [nio-8080-exec-3] org.zalando.logbook.Logbook.write#30              : {"body":{"code":200,"message":"操作成功","data":{"authUrl":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=http%3A%2F%2Ft44af468.natappfree.cc%2Flogin&response_type=code&scope=snsapi_userinfo&state=3P36UjFXZdgSXRzE&connect_redirect=1#wechat_redirect"},"error":false,"success":true},"correlation":"c036a26f774fa1c6","duration":"3","headers":{"Content-Type":["application/json"]},"origin":"local","protocol":"HTTP/1.0","status":200,"type":"response"}
2025-08-05 21:11:18.990 TRACE  689202f61f044211a3208a53e19a9a58 [nio-8080-exec-2] org.zalando.logbook.Logbook.write#25              : {"body":{"redirectUri":"https://val.jcyeah.com/login","state":"A9D9bFZso0dzOXi4"},"correlation":"bcce6c578acaf456","headers":{"content-type":["application/json"],"user-agent":["Mozilla/5.0 (iPhone; CPU iPhone OS 19_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d34) NetType/WIFI Language/zh_CN"],"x-real-ip":["127.0.0.1"]},"host":"t44af468.natappfree.cc","method":"POST","origin":"remote","path":"/wechat/auth-url","port":"80","protocol":"HTTP/1.0","remote":"0:0:0:0:0:0:0:1","scheme":"http","type":"request","uri":"http://t44af468.natappfree.cc/wechat/auth-url"}
2025-08-05 21:11:18.991  INFO  689202f61f044211a3208a53e19a9a58 [nio-8080-exec-2] c.czp.wwqy.bus.service.WechatService.getAuthUrl#43: 生成微信授权URL成功: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=https%3A%2F%2Fval.jcyeah.com%2Flogin&response_type=code&scope=snsapi_userinfo&state=A9D9bFZso0dzOXi4&connect_redirect=1#wechat_redirect
2025-08-05 21:11:18.995 TRACE  689202f61f044211a3208a53e19a9a58 [nio-8080-exec-2] org.zalando.logbook.Logbook.write#30              : {"body":{"code":200,"message":"操作成功","data":{"authUrl":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=https%3A%2F%2Fval.jcyeah.com%2Flogin&response_type=code&scope=snsapi_userinfo&state=A9D9bFZso0dzOXi4&connect_redirect=1#wechat_redirect"},"error":false,"success":true},"correlation":"bcce6c578acaf456","duration":"3","headers":{"Content-Type":["application/json"]},"origin":"local","protocol":"HTTP/1.0","status":200,"type":"response"}
2025-08-05 21:11:20.987 TRACE  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] org.zalando.logbook.Logbook.write#25              : {"body":{"code":"061wOk100GVBKU1m6T100CQZp93wOk1x","state":"A9D9bFZso0dzOXi4"},"correlation":"da0627df718dcec7","headers":{"content-type":["application/json"],"user-agent":["Mozilla/5.0 (iPhone; CPU iPhone OS 19_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d34) NetType/WIFI Language/zh_CN"],"x-real-ip":["127.0.0.1"]},"host":"t44af468.natappfree.cc","method":"POST","origin":"remote","path":"/wechat/login","port":"80","protocol":"HTTP/1.0","remote":"127.0.0.1","scheme":"http","type":"request","uri":"http://t44af468.natappfree.cc/wechat/login"}
2025-08-05 21:11:21.886  INFO  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] qy.bus.service.WechatService.getUserInfoByCode#130: 获取微信用户信息成功，OpenID: oPe9B5mKrvy0aKYRECSl7v_dgZhc
2025-08-05 21:11:22.028  INFO  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] m.zaxxer.hikari.HikariDataSource.getConnection#109: HikariPool-1 - Starting...
2025-08-05 21:11:22.292  INFO  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] om.zaxxer.hikari.pool.HikariPool.checkFailFast#580: HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@380d0c1a
2025-08-05 21:11:22.297  INFO  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] m.zaxxer.hikari.HikariDataSource.getConnection#122: HikariPool-1 - Start completed.
2025-08-05 21:11:22.394  INFO  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] c.czp.wwqy.bus.service.WechatService.login#66     : 用户已存在，更新用户信息：WxUser(id=1, openid=oPe9B5mKrvy0aKYRECSl7v_dgZhc, unionid=null, nickname=CZP, avatarUrl=https://thirdwx.qlogo.cn/mmopen/vi_32/gd5AevQcyvNUvusD0CicHk3SzmbJCRdSvaTUHoa4W9PLJdc3YAU5VSACqibGc6yb5KsyRCV5RrkoZOFT4hQ1XPcw/132, gender=0, country=, province=, city=, language=zh_CN, subscribe=null, subscribeTime=null, createTime=null, updateTime=2025-08-05T21:11:22.375146300, deletedFlag=0)
2025-08-05 21:11:22.403  INFO  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] c.czp.wwqy.bus.service.WechatService.login#71     : 微信登录成功，OpenID: oPe9B5mKrvy0aKYRECSl7v_dgZhc
2025-08-05 21:11:22.408 TRACE  689202f8d0cde2762485d746f7e52e5d [nio-8080-exec-4] org.zalando.logbook.Logbook.write#30              : {"body":{"code":200,"message":"操作成功","data":{"userInfo":{"id":"1","openid":"oPe9B5mKrvy0aKYRECSl7v_dgZhc","nickname":"CZP","avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/gd5AevQcyvNUvusD0CicHk3SzmbJCRdSvaTUHoa4W9PLJdc3YAU5VSACqibGc6yb5KsyRCV5RrkoZOFT4hQ1XPcw/132","gender":0,"country":"","province":"","city":"","language":"zh_CN","updateTime":"2025-08-05 21:11:22","deletedFlag":0},"loginTime":"1754399482404"},"error":false,"success":true},"correlation":"da0627df718dcec7","duration":"1420","headers":{"Content-Type":["application/json"]},"origin":"local","protocol":"HTTP/1.0","status":200,"type":"response"}
2025-08-05 21:19:09.543 TRACE  689204cd7614cc08fdfb9165740f13d0 [nio-8080-exec-5] org.zalando.logbook.Logbook.write#25              : {"body":{"redirectUri":"https://val.jcyeah.com/login","state":"KkZqx56tycjbGjj6"},"correlation":"c7fc015a5018c6c1","headers":{"content-type":["application/json"],"user-agent":["Mozilla/5.0 (iPhone; CPU iPhone OS 19_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d34) NetType/WIFI Language/zh_CN"],"x-real-ip":["127.0.0.1"]},"host":"t44af468.natappfree.cc","method":"POST","origin":"remote","path":"/wechat/auth-url","port":"80","protocol":"HTTP/1.0","remote":"0:0:0:0:0:0:0:1","scheme":"http","type":"request","uri":"http://t44af468.natappfree.cc/wechat/auth-url"}
2025-08-05 21:19:09.544  INFO  689204cd7614cc08fdfb9165740f13d0 [nio-8080-exec-5] c.czp.wwqy.bus.service.WechatService.getAuthUrl#43: 生成微信授权URL成功: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=https%3A%2F%2Fval.jcyeah.com%2Flogin&response_type=code&scope=snsapi_userinfo&state=KkZqx56tycjbGjj6&connect_redirect=1#wechat_redirect
2025-08-05 21:19:09.545 TRACE  689204cd7614cc08fdfb9165740f13d0 [nio-8080-exec-5] org.zalando.logbook.Logbook.write#30              : {"body":{"code":200,"message":"操作成功","data":{"authUrl":"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxdac140cf05115764&redirect_uri=https%3A%2F%2Fval.jcyeah.com%2Flogin&response_type=code&scope=snsapi_userinfo&state=KkZqx56tycjbGjj6&connect_redirect=1#wechat_redirect"},"error":false,"success":true},"correlation":"c7fc015a5018c6c1","duration":"3","headers":{"Content-Type":["application/json"]},"origin":"local","protocol":"HTTP/1.0","status":200,"type":"response"}
2025-08-05 21:19:11.560 TRACE  689204cff5d5588be026501ae5584e8c [nio-8080-exec-6] org.zalando.logbook.Logbook.write#25              : {"body":{"code":"041G801w3IQyo5313y2w3UsFdj2G801t","state":"KkZqx56tycjbGjj6"},"correlation":"99cfddfe9314cbc4","headers":{"content-type":["application/json"],"user-agent":["Mozilla/5.0 (iPhone; CPU iPhone OS 19_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.61(0x18003d34) NetType/WIFI Language/zh_CN"],"x-real-ip":["127.0.0.1"]},"host":"t44af468.natappfree.cc","method":"POST","origin":"remote","path":"/wechat/login","port":"80","protocol":"HTTP/1.0","remote":"127.0.0.1","scheme":"http","type":"request","uri":"http://t44af468.natappfree.cc/wechat/login"}
2025-08-05 21:19:12.138  INFO  689204cff5d5588be026501ae5584e8c [nio-8080-exec-6] qy.bus.service.WechatService.getUserInfoByCode#130: 获取微信用户信息成功，OpenID: oPe9B5mKrvy0aKYRECSl7v_dgZhc
2025-08-05 21:19:12.212  INFO  689204cff5d5588be026501ae5584e8c [nio-8080-exec-6] c.czp.wwqy.bus.service.WechatService.login#66     : 用户已存在，更新用户信息：WxUser(id=1, openid=oPe9B5mKrvy0aKYRECSl7v_dgZhc, unionid=null, nickname=CZP, avatarUrl=https://thirdwx.qlogo.cn/mmopen/vi_32/gd5AevQcyvNUvusD0CicHk3SzmbJCRdSvaTUHoa4W9PLJdc3YAU5VSACqibGc6yb5KsyRCV5RrkoZOFT4hQ1XPcw/132, gender=0, country=, province=, city=, language=zh_CN, subscribe=null, subscribeTime=null, createTime=null, updateTime=2025-08-05T21:19:12.190473300, deletedFlag=0)
2025-08-05 21:19:12.213  INFO  689204cff5d5588be026501ae5584e8c [nio-8080-exec-6] c.czp.wwqy.bus.service.WechatService.login#71     : 微信登录成功，OpenID: oPe9B5mKrvy0aKYRECSl7v_dgZhc
2025-08-05 21:19:12.215 TRACE  689204cff5d5588be026501ae5584e8c [nio-8080-exec-6] org.zalando.logbook.Logbook.write#30              : {"body":{"code":200,"message":"操作成功","data":{"userInfo":{"id":"1","openid":"oPe9B5mKrvy0aKYRECSl7v_dgZhc","nickname":"CZP","avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/gd5AevQcyvNUvusD0CicHk3SzmbJCRdSvaTUHoa4W9PLJdc3YAU5VSACqibGc6yb5KsyRCV5RrkoZOFT4hQ1XPcw/132","gender":0,"country":"","province":"","city":"","language":"zh_CN","updateTime":"2025-08-05 21:19:12","deletedFlag":0},"loginTime":"1754399952213"},"error":false,"success":true},"correlation":"99cfddfe9314cbc4","duration":"654","headers":{"Content-Type":["application/json"]},"origin":"local","protocol":"HTTP/1.0","status":200,"type":"response"}
2025-08-05 21:26:46.801  INFO   [ionShutdownHook] .w.e.tomcat.GracefulShutdown.shutDownGracefully#54: Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 21:26:47.423  INFO   [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown.doShutdown#76   : Graceful shutdown complete
2025-08-05 21:26:47.432  INFO   [ionShutdownHook] com.zaxxer.hikari.HikariDataSource.close#349      : HikariPool-1 - Shutdown initiated...
2025-08-05 21:26:47.440  INFO   [ionShutdownHook] com.zaxxer.hikari.HikariDataSource.close#351      : HikariPool-1 - Shutdown completed.
