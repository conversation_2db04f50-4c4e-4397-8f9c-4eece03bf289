<template>
  <div class="area6-view">

    <!-- 暂未开放提示 -->
    <div class="coming-soon-container">
      <div class="coming-soon-content">
        <van-icon name="photo-o" size="64" color="#1989fa" />
        <h2 class="text-24 font-bold text-gray-800 mt-16 mb-8">官方相册</h2>
        <p class="text-16 text-gray-600 mb-24">功能即将上线，敬请期待！</p>
        <van-button type="primary" @click="goBack">
          返回首页
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回首页
const goBack = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.area6-view {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.coming-soon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 40px 20px;
}

.coming-soon-content {
  text-align: center;
  background: white;
  padding: 40px 30px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 320px;
  width: 100%;
}

// 区域6特有的样式（官方相册主题）
// 可以添加相册相关的视觉效果
</style>