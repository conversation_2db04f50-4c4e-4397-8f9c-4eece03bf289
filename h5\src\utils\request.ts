import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { showToast, showLoadingToast, closeToast } from 'vant'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { storage } from './storage'

// 配置NProgress
NProgress.configure({
  showSpinner: false,
  minimum: 0.2,
  speed: 500
})

// 统一返回体接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  showProgress?: boolean
}

// 基础请求URL
// 开发环境：空字符串，让vite proxy处理/api前缀
// 生产环境：实际的后端服务器地址
const BASE_URL = import.meta.env.VITE_API_BASE_URL || ''

// 请求超时时间
const TIMEOUT = 10000

// 创建axios实例
const axiosInstance: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    // 从storage获取token
    const token = storage.local.get('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response
  },
  (error) => {
    // 处理HTTP错误状态码
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 401:
          // 未授权，清除token并跳转登录
          storage.local.remove('token')
          storage.local.remove('userInfo')
          window.location.href = '/login'
          break
        case 403:
          showToast({ message: '权限不足', type: 'fail' })
          break
        case 404:
          showToast({ message: '请求的资源不存在', type: 'fail' })
          break
        case 500:
          showToast({ message: '服务器内部错误', type: 'fail' })
          break
        default:
          showToast({
            message: data?.message || `请求失败 (${status})`,
            type: 'fail'
          })
      }
    } else if (error.code === 'ECONNABORTED') {
      showToast({ message: '请求超时', type: 'fail' })
    } else {
      showToast({ message: '网络错误', type: 'fail' })
    }

    return Promise.reject(error)
  }
)

/**
 * 统一请求方法
 * @param config 请求配置
 * @returns Promise<ApiResponse<T>>
 */
export const request = async <T = any>(config: RequestConfig): Promise<ApiResponse<T>> => {
  const {
    showLoading = false,
    showError = true,
    showProgress = true,
    ...axiosConfig
  } = config

  // 显示进度条
  if (showProgress) {
    NProgress.start()
  }

  // 显示加载提示
  if (showLoading) {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0
    })
  }

  try {
    const response = await axiosInstance.request<ApiResponse<T>>(axiosConfig)
    const result = response.data

    // 关闭加载提示
    if (showLoading) {
      closeToast()
    }

    // 关闭进度条
    if (showProgress) {
      NProgress.done()
    }

    // 处理业务错误
    if (result.code !== 200) {
      if (showError) {
        showToast({
          message: result.message || '请求失败',
          type: 'fail'
        })
      }
      throw new Error(result.message || '请求失败')
    }

    return result
  } catch (error) {
    // 关闭加载提示
    if (showLoading) {
      closeToast()
    }

    // 关闭进度条
    if (showProgress) {
      NProgress.done()
    }

    // 如果是axios错误且已经在拦截器中处理过，不再重复显示错误
    if (axios.isAxiosError(error) && error.response) {
      throw error
    }

    // 处理其他错误
    if (error instanceof Error) {
      if (showError) {
        showToast({
          message: error.message || '请求失败',
          type: 'fail'
        })
      }
      throw error
    }

    const message = '未知错误'
    if (showError) {
      showToast({ message, type: 'fail' })
    }
    throw new Error(message)
  }
}

/**
 * GET请求
 */
export const get = <T = any>(url: string, params?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data' | 'params'>) => {
  return request<T>({
    url,
    method: 'GET',
    params,
    ...config
  })
}

/**
 * POST请求
 */
export const post = <T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data'>) => {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...config
  })
}

/**
 * PUT请求
 */
export const put = <T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data'>) => {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...config
  })
}

/**
 * DELETE请求
 */
export const del = <T = any>(url: string, params?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data' | 'params'>) => {
  return request<T>({
    url,
    method: 'DELETE',
    params,
    ...config
  })
}

// 导出axios实例，供特殊需求使用
export { axiosInstance as axios }