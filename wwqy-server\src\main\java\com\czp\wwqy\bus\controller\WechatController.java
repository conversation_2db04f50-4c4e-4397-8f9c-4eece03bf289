package com.czp.wwqy.bus.controller;

import com.czp.wwqy.bus.dto.*;
import com.czp.wwqy.bus.service.WechatService;
import com.czp.wwqy.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微信控制器
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/wechat")
@RequiredArgsConstructor
public class WechatController {

    private final WechatService wechatService;

    /**
     * 获取微信授权URL
     *
     * @param request 请求参数
     * @return 授权URL
     */
    @PostMapping("/auth-url")
    public Result<WechatAuthUrlResponse> getAuthUrl(@Validated @RequestBody WechatAuthUrlRequest request) {
        WechatAuthUrlResponse response = wechatService.getAuthUrl(request);
        return Result.ok(response);
    }

    /**
     * 微信授权登录
     *
     * @param request 请求参数
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<WechatLoginResponse> login(@Validated @RequestBody WechatLoginRequest request) {
        WechatLoginResponse response = wechatService.login(request);
        return Result.ok(response);
    }

    /**
     * 获取微信二维码ticket
     *
     * @param request 请求参数
     * @return 二维码信息
     */
    @PostMapping("/qrcode-ticket")
    public Result<QrCodeTicketResponse> getQrCodeTicket(@Validated @RequestBody QrCodeTicketRequest request) {
        QrCodeTicketResponse response = wechatService.getQrCodeTicket(request);
        return Result.ok(response);
    }

    /**
     * 获取微信JS-SDK配置
     *
     * @param request 请求参数
     * @return JS-SDK配置信息
     */
    @PostMapping("/config")
    public Result<WechatJsConfigResponse> getJsConfig(@Validated @RequestBody WechatJsConfigRequest request) {
        WechatJsConfigResponse response = wechatService.getJsConfig(request);
        return Result.ok(response);
    }

}