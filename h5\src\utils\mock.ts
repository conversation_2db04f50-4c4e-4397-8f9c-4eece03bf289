/**
 * 模拟数据工具
 */

// 模拟用户数据
export const mockUserData = {
  id: 1,
  openid: "oPe9B5mKrvy0aKYRECSl7v_dgZhc",
  unionid: null,
  nickname: "<PERSON>Z<PERSON>",
  avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/gd5AevQcyvNUvusD0CicHk3SzmbJCRdSvaTUHoa4W9PLJdc3YAU5VSACqibGc6yb5KsyRCV5RrkoZOFT4hQ1XPcw/132",
  gender: 0,
  country: "",
  province: "",
  city: "",
  language: "zh_CN",
  subscribe: 0,
  subscribe_time: null,
  create_time: "2025-07-27 18:14:26",
  update_time: "2025-07-28 15:47:12",
  deleted_flag: 0
}

// 模拟登录响应数据
export const mockLoginResponse = {
  code: 200,
  message: "登录成功",
  data: {
    userInfo: mockUserData,
    token: "mock_token_" + Date.now(),
    expiresIn: 30 * 24 * 60 * 60 * 1000 // 30天
  }
}

// 检查是否启用模拟模式
export const isMockMode = (): boolean => {
  return import.meta.env.VITE_MOCK_MODE === 'true' || 
         localStorage.getItem('mock_mode') === 'true' ||
         window.location.search.includes('mock=true')
}

// 启用/禁用模拟模式
export const setMockMode = (enabled: boolean): void => {
  localStorage.setItem('mock_mode', enabled.toString())
  console.log(`模拟模式已${enabled ? '启用' : '禁用'}`)
}

// 模拟延迟
export const mockDelay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 模拟微信授权URL
export const mockWechatAuthUrl = "javascript:void(0)"

// 模拟微信授权code
export const mockWechatCode = "mock_code_" + Date.now()

// 模拟微信授权state
export const mockWechatState = "mock_state_" + Date.now()

// 模拟JSSDK配置
export const mockJSSDKConfig = {
  appId: "mock_app_id",
  timestamp: Math.floor(Date.now() / 1000),
  nonceStr: "mock_nonce_str",
  signature: "mock_signature",
  jsApiList: [
    'scanQRCode',
    'updateTimelineShareData',
    'updateAppMessageShareData'
  ]
}

// 全局模拟开关
if (typeof window !== 'undefined') {
  // 添加全局方法用于控制台调试
  (window as any).enableMock = () => {
    setMockMode(true)
  }
  (window as any).disableMock = () => {
    setMockMode(false)
  }
  (window as any).checkMockMode = () => {
    console.log('模拟模式状态:', isMockMode())
    return isMockMode()
  }
}
