package com.czp.wwqy.bus.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wx_user")
public class WxUser {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 微信OpenID
     */
    @TableField("openid")
    private String openid;

    /**
     * 微信UnionID
     */
    @TableField("unionid")
    private String unionid;

    /**
     * 用户昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 用户头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 用户性别 0-未知 1-男 2-女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 用户所在国家
     */
    @TableField("country")
    private String country;

    /**
     * 用户所在省份
     */
    @TableField("province")
    private String province;

    /**
     * 用户所在城市
     */
    @TableField("city")
    private String city;

    /**
     * 用户语言
     */
    @TableField("language")
    private String language;

    /**
     * 是否关注公众号 0-未关注 1-已关注
     */
    @TableField("subscribe")
    private Integer subscribe;

    /**
     * 关注时间
     */
    @TableField("subscribe_time")
    private LocalDateTime subscribeTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableLogic
    @TableField("deleted_flag")
    private Integer deletedFlag = 0;
}