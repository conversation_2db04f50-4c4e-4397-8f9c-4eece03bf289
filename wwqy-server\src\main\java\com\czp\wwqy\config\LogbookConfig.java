package com.czp.wwqy.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zalando.logbook.HeaderFilter;
import org.zalando.logbook.core.HeaderFilters;

import java.util.Arrays;
import java.util.List;

/**
 * Logbook配置
 *
 * <AUTHOR>
 * @date Created in 2025/4/8 17:46
 */
@Configuration
public class LogbookConfig {

    @Bean
    public HeaderFilter headerFilter() {
        List<String> headNames = Arrays.asList("x-real-ip", "content-type", "x-userid", "x-login-type", "x-test", "user-agent");
        return HeaderFilters.removeHeaders(header -> !headNames.contains(header.toLowerCase()));
    }

}