package com.czp.wwqy.config;

import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 微信配置类
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Configuration
public class WechatConfig {

    @Value("${wechat.appId:wxdac140cf05115764}")
    private String appId;

    @Value("${wechat.appSecret:d95abff85d77cb791d8daecfae8d610f}")
    private String appSecret;

    @Bean
    public WxMpService wxMpService() {
        WxMpDefaultConfigImpl config = new WxMpDefaultConfigImpl();
        config.setAppId(appId);
        config.setSecret(appSecret);
        
        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(config);
        
        return wxMpService;
    }
}