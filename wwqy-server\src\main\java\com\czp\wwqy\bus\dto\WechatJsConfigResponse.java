package com.czp.wwqy.bus.dto;

import lombok.Data;

import java.util.List;

/**
 * 微信JS-SDK配置响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Data
public class WechatJsConfigResponse {

    /**
     * 公众号的唯一标识
     */
    private String appId;

    /**
     * 生成签名的时间戳
     */
    private Long timestamp;

    /**
     * 生成签名的随机串
     */
    private String nonceStr;

    /**
     * 签名
     */
    private String signature;

    /**
     * 需要使用的JS接口列表
     */
    private List<String> jsApiList;

    public WechatJsConfigResponse(String appId, Long timestamp, String nonceStr, String signature, List<String> jsApiList) {
        this.appId = appId;
        this.timestamp = timestamp;
        this.nonceStr = nonceStr;
        this.signature = signature;
        this.jsApiList = jsApiList;
    }
}