# 点击区域设计工具使用指南

## 🎯 工具概述

这是一个专门用于精确绘制HomeView.vue中6个点击区域的可视化设计工具，基于Fabric.js构建，支持通过鼠标点击的方式在背景图片上标记点位，连接多个点位形成闭合路径，定义不规则的点击区域。

## 🚀 功能特性

### 核心功能
- ✅ **可视化绘制**: 在背景图片上直接点击绘制路径点
- ✅ **多区域管理**: 支持6个独立的点击区域设计
- ✅ **实时预览**: 实时显示绘制的路径和区域效果
- ✅ **精确定位**: 支持拖拽调整路径点位置
- ✅ **智能导出**: 自动生成Vue代码和配置数据

### 交互功能
- ✅ **双模式切换**: 绘制模式 ↔ 预览模式
- ✅ **撤销/重做**: 完整的操作历史记录
- ✅ **右键删除**: 右键点击路径点快速删除
- ✅ **拖拽移动**: 绘制模式下可拖拽调整点位置
- ✅ **区域切换**: 快速切换编辑不同区域

### 导出功能
- ✅ **SVG导出**: 导出完整的SVG矢量图形
- ✅ **Vue代码导出**: 生成可直接使用的Vue组件代码
- ✅ **JSON数据导出**: 导出结构化的配置数据

## 📍 访问方式

### 方法一：直接访问
```
http://localhost:5173/design
```

### 方法二：从首页跳转
1. 访问首页 `http://localhost:5173/`
2. 开发环境下右上角会显示调试面板
3. 点击"设计工具"按钮跳转

## 🎨 界面布局

### 顶部工具栏
- **页面标题**: 点击区域设计工具
- **当前区域**: 显示正在编辑的区域编号
- **模式切换**: 绘制模式 ↔ 预览模式
- **操作按钮**: 撤销、重做、清空当前、清空全部

### 左侧画布区域
- **背景图片**: 自动加载 `src/assets/images/2.jpg`
- **绘制画布**: 基于Fabric.js的交互式画布
- **操作提示**: 详细的使用说明

### 右侧控制面板
- **区域管理**: 6个区域的快速切换
- **当前区域信息**: 显示路径点数和边界框
- **坐标信息**: 实时显示所有路径点坐标
- **导出功能**: 多种格式的导出选项

## 🖱️ 操作指南

### 基础绘制流程
1. **选择区域**: 右侧面板选择要编辑的区域（1-6）
2. **进入绘制模式**: 确保顶部显示"绘制模式"
3. **添加路径点**: 在画布上点击添加路径点
4. **形成闭合路径**: 至少添加3个点
5. **完成绘制**: 双击画布完成当前区域
6. **自动切换**: 系统自动切换到下一个区域

### 高级操作技巧
- **精确调整**: 绘制模式下拖拽路径点调整位置
- **快速删除**: 右键点击路径点删除
- **批量删除**: 使用坐标信息面板的删除按钮
- **预览效果**: 切换到预览模式查看最终效果
- **撤销操作**: 使用撤销/重做功能修正错误

### 区域管理
```
区域1 - 红色 (#ff4444)
区域2 - 绿色 (#44ff44)  
区域3 - 蓝色 (#4444ff)
区域4 - 黄色 (#ffff44)
区域5 - 紫色 (#ff44ff)
区域6 - 青色 (#44ffff)
```

## 📤 导出功能详解

### 1. SVG导出
```xml
<!-- 导出完整的SVG矢量图形 -->
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">
  <!-- 背景图片和所有绘制的路径 -->
</svg>
```

### 2. Vue代码导出
```typescript
// 点击区域配置
const clickAreas = ref([
  // 区域1
  { left: '15.50%', top: '25.30%', width: '18.20%', height: '12.40%' },
  // 区域2
  { left: '66.30%', top: '25.30%', width: '18.20%', height: '12.40%' },
  // ... 更多区域
])

// 详细路径点数据（用于精确碰撞检测）
const areaPoints = ref([
  // 区域1路径点
  [
    { x: 15.50, y: 25.30 },
    { x: 33.70, y: 25.30 },
    // ... 更多点
  ],
  // ... 更多区域
])
```

### 3. JSON数据导出
```json
{
  "canvasSize": { "width": 800, "height": 600 },
  "imageSize": { "width": 1920, "height": 1080 },
  "areas": [
    {
      "name": "区域1",
      "color": "#ff4444",
      "points": [
        { "x": 124, "y": 152 },
        { "x": 270, "y": 152 }
      ],
      "percentagePoints": [
        { "x": 15.50, "y": 25.30 },
        { "x": 33.75, "y": 25.30 }
      ]
    }
  ]
}
```

## 🔧 技术实现

### 核心技术栈
- **Vue 3**: 响应式框架
- **TypeScript**: 类型安全
- **Fabric.js**: 画布交互库
- **UnoCSS**: 原子化CSS
- **Vant**: UI组件库

### 关键特性
- **响应式设计**: 自适应不同屏幕尺寸
- **实时计算**: 自动计算百分比坐标
- **历史记录**: 完整的撤销/重做功能
- **类型安全**: 完整的TypeScript类型定义

### 坐标转换
```typescript
// 像素坐标 → 百分比坐标
const percentageX = (pixelX / canvasWidth) * 100
const percentageY = (pixelY / canvasHeight) * 100

// 边界框计算
const boundingBox = {
  left: Math.min(...xCoords),
  top: Math.min(...yCoords),
  width: Math.max(...xCoords) - Math.min(...xCoords),
  height: Math.max(...yCoords) - Math.min(...yCoords)
}
```

## 🎯 使用场景

### 1. 初次设计
- 加载背景图片
- 逐个绘制6个点击区域
- 导出Vue代码应用到HomeView.vue

### 2. 精确调整
- 导入现有配置数据
- 微调路径点位置
- 重新导出更新代码

### 3. 效果预览
- 切换到预览模式
- 查看所有区域的最终效果
- 确认区域位置和大小

## 📋 最佳实践

### 绘制建议
1. **从左上角开始**: 按顺时针方向绘制路径点
2. **保持适当密度**: 每个区域3-8个路径点为宜
3. **避免交叉路径**: 确保路径不自相交
4. **预留边距**: 考虑触摸设备的点击精度

### 导出建议
1. **先预览再导出**: 确认效果后再导出代码
2. **备份原始数据**: 保存JSON数据用于后续调整
3. **测试兼容性**: 在不同设备上测试点击效果

### 维护建议
1. **版本控制**: 将设计数据纳入版本控制
2. **文档记录**: 记录每个区域的功能说明
3. **定期检查**: 定期检查区域位置是否准确

## 🚀 集成到项目

### 1. 复制导出的Vue代码
```typescript
// 替换HomeView.vue中的clickAreas配置
const clickAreas = ref([
  // 粘贴导出的区域配置
])
```

### 2. 更新点击处理逻辑
```typescript
// 可选：使用精确的路径点进行碰撞检测
const isPointInArea = (x: number, y: number, areaIndex: number) => {
  const points = areaPoints.value[areaIndex]
  // 实现点在多边形内的判断算法
}
```

### 3. 测试验证
- 在不同设备上测试点击响应
- 验证区域边界的准确性
- 确认用户体验符合预期

现在您可以使用这个强大的可视化工具来精确设计HomeView.vue中的点击区域了！🎉
