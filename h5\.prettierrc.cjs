module.exports = {
  // 指定每行的最大长度
  // 建议设置为 80 或 100，可以在代码审查中保持较好的可读性
  printWidth: 130,

  // 缩进的空格数
  // 默认是 2，常用设置为 2 或 4，根据项目的风格调整
  tabWidth: 2,

  // 是否使用 Tab 代替空格
  // true：使用 Tab，false：使用空格
  useTabs: false,

  // 是否在语句后添加分号
  // true：添加分号，false：不添加分号
  semi: false,

  // 是否使用单引号
  // true：使用单引号，false：使用双引号
  singleQuote: true,

  // 对象属性中的引号是否强制一致
  // "as-needed"：仅在必要时加引号，"consistent"：一致地使用引号，"preserve"：保持原样
  quoteProps: 'as-needed',

  // 在 JSX 中是否使用单引号
  // true：使用单引号，false：使用双引号
  jsxSingleQuote: false,

  // 是否在多行数组、对象中末尾添加逗号
  // "none"：不添加，"es5"：仅在 ES5 有效语法中添加，"all"：在所有地方添加
  trailingComma: 'es5',

  // 对象大括号内是否保留空格
  // true：添加空格，例如 { foo: bar }，false：不添加空格，例如 {foo: bar}
  bracketSpacing: true,

  // JSX 标签的右尖括号是否单独成行
  // true：单独成行，false：不单独成行
  jsxBracketSameLine: false,

  // 箭头函数的参数是否始终加括号
  // "always"：始终加括号，"avoid"：在可省略的情况下省略括号
  arrowParens: 'always',

  // 在文件顶部添加 Pragma 标识，用于文件格式化控制
  // true：添加 Pragma，false：不添加
  requirePragma: false,

  // 在文件顶部插入 Pragma 标识时自动进行格式化
  // true：自动格式化，false：不自动格式化
  insertPragma: false,

  // 是否根据文件内容自动换行
  // "auto"：自动换行，"lf"：换行符为 LF，"crlf"：换行符为 CRLF，"cr"：换行符为 CR
  endOfLine: 'lf',

  // HTML 中是否强制换行
  // true：强制换行，false：不强制换行
  htmlWhitespaceSensitivity: 'css',

  // Vue 文件中的 script 和 style 标签是否缩进
  // true：缩进，false：不缩进
  vueIndentScriptAndStyle: false,

  // 是否在 Markdown 文件中包装文本
  // "always"：始终包装，"never"：不包装，"preserve"：保持现有的包装
  proseWrap: 'preserve',
}
