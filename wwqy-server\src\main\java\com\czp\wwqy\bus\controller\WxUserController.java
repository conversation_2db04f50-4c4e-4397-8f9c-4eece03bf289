package com.czp.wwqy.bus.controller;

import com.czp.wwqy.bus.entity.WxUser;
import com.czp.wwqy.bus.service.WxUserService;
import com.czp.wwqy.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2025-07-27
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class WxUserController {

    private final WxUserService wxUserService;

    /**
     * 根据OpenID获取用户信息
     *
     * @param openid 微信OpenID
     * @return 用户信息
     */
    @GetMapping("/info/{openid}")
    public Result<WxUser> getUserInfo(@PathVariable String openid) {
        log.info("获取用户信息，OpenID: {}", openid);
        WxUser wxUser = wxUserService.getByOpenid(openid);
        return Result.ok(wxUser);
    }

}