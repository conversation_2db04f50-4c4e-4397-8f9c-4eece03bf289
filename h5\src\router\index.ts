import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import Area1View from '../views/area/Area1View.vue'
import Area2View from '../views/area/Area2View.vue'
import Area3View from '../views/area/Area3View.vue'
import Area4View from '../views/area/Area4View.vue'
import Area5View from '../views/area/Area5View.vue'
import Area6View from '../views/area/Area6View.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { title: '无畏契约周年庆 - 首页' },
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { title: '无畏契约周年庆 - 登录' },
    },
    {
      path: '/area1',
      name: 'area1',
      component: Area1View,
      meta: { title: '无畏契约周年庆 - 竞技空间' },
    },
    {
      path: '/area2',
      name: 'area2',
      component: Area2View,
      meta: { title: '无畏契约周年庆 - 打瓦空间' },
    },
    {
      path: '/area3',
      name: 'area3',
      component: Area3View,
      meta: { title: '无畏契约周年庆 - 制爆街区' },
    },
    {
      path: '/area4',
      name: 'area4',
      component: Area4View,
      meta: { title: '无畏契约周年庆 - 打卡地图' },
    },
    {
      path: '/area5',
      name: 'area5',
      component: Area5View,
      meta: { title: '无畏契约周年庆 - 惊喜瓦礼' },
    },
    {
      path: '/area6',
      name: 'area6',
      component: Area6View,
      meta: { title: '无畏契约周年庆 - 官方相册' },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }

  // 检查登录状态（仅在客户端）
  if (typeof window !== 'undefined') {
    // 动态导入用户store以避免SSR问题
    import('@/stores/user')
      .then(({ useUserStore }) => {
        const userStore = useUserStore()

        // 如果访问首页但未登录，重定向到登录页
        if (to.path === '/' && !userStore.isLoggedIn) {
          next('/login')
          return
        }

        // 如果访问登录页但已登录，重定向到首页
        if (to.path === '/login' && userStore.isLoggedIn) {
          next('/')
          return
        }

        // 如果访问area页面但未登录，重定向到登录页
        if (to.path.startsWith('/area') && !userStore.isLoggedIn) {
          next('/login')
          return
        }

        next()
      })
      .catch(() => {
        // 如果store加载失败，直接放行
        next()
      })
  } else {
    next()
  }
})

export default router