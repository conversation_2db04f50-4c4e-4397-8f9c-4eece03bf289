# NATAPP端口配置问题解决方案

## 问题分析
您遇到的500错误是因为natapp配置的端口不匹配：
- **natapp配置**: `http://kcf6c27c.natappfree.cc -> 127.0.0.1:80`
- **开发服务器**: 运行在 `127.0.0.1:3000`

当微信授权完成重定向回来时，natapp尝试访问80端口，但没有服务在监听，所以报500错误。

## 解决方案

### 方案1：修改natapp配置（推荐）

**步骤1**: 停止当前的natapp进程

**步骤2**: 重新启动natapp，指定3000端口
```bash
# 如果使用命令行启动
natapp -authtoken=your_token -proto=http -addr=3000

# 或者如果有配置文件，修改配置文件中的addr字段
[tunnel]
authtoken=your_auth_token
proto=http
addr=3000
subdomain=kcf6c27c
```

**步骤3**: 确认映射正确
启动后应该显示：`http://kcf6c27c.natappfree.cc -> 127.0.0.1:3000`

### 方案2：使用代理服务器

如果无法修改natapp配置，可以使用我创建的代理服务器：

**步骤1**: 确保Vite开发服务器正在运行
```bash
cd h5
pnpm run dev
```

**步骤2**: 以管理员权限启动代理服务器
```bash
# Windows (以管理员身份运行PowerShell)
cd h5
node proxy-server.js

# Linux/Mac
sudo node proxy-server.js
```

**步骤3**: 确认代理服务器启动成功
应该看到类似输出：
```
代理服务器启动成功！
监听端口: 80
代理目标: http://127.0.0.1:3000
外网访问: http://kcf6c27c.natappfree.cc
```

## 测试步骤

### 1. 确认服务器状态
```bash
# 检查3000端口是否有服务
curl http://127.0.0.1:3000

# 检查80端口是否有服务（如果使用代理）
curl http://127.0.0.1:80
```

### 2. 测试外网访问
在浏览器中访问：`http://kcf6c27c.natappfree.cc`
应该能看到您的H5页面

### 3. 测试微信授权
1. 在微信中打开：`http://kcf6c27c.natappfree.cc`
2. 点击"微信授权登录"
3. 确认授权后应该能正常回到首页并完成登录

## 推荐配置

### 开发环境最佳实践
1. **修改natapp配置为3000端口**（最简单）
2. 保持Vite开发服务器默认配置
3. 不需要额外的代理服务器

### 配置文件示例
如果您使用natapp配置文件，应该类似这样：
```ini
[tunnel]
authtoken=your_auth_token_here
proto=http
addr=3000
subdomain=kcf6c27c
```

## 常见问题

### Q: 为什么不直接修改Vite端口为80？
A: 80端口需要管理员权限，而且可能与系统服务冲突。修改natapp配置更简单安全。

### Q: 代理服务器需要一直运行吗？
A: 如果使用代理方案，需要同时运行Vite开发服务器和代理服务器。推荐直接修改natapp配置。

### Q: 修改natapp配置后需要重新配置微信公众号吗？
A: 不需要，域名部分没有变化，只是内部端口映射改变了。

## 验证清单

完成配置后，请验证以下项目：
- [ ] natapp显示正确的端口映射（3000端口）
- [ ] 本地可以访问 http://127.0.0.1:3000
- [ ] 外网可以访问 http://kcf6c27c.natappfree.cc
- [ ] 微信中可以正常打开页面
- [ ] 微信授权登录流程正常
- [ ] 授权成功后能正常显示用户信息

## 下一步

配置完成后，您就可以：
1. 在微信中访问 `http://kcf6c27c.natappfree.cc`
2. 点击"微信授权登录"
3. 授权成功后在首页看到您的微信头像和昵称
4. 开始测试其他功能

如果还有问题，请检查：
1. natapp是否正常运行
2. Vite开发服务器是否正常运行
3. 端口映射是否正确
4. 防火墙是否阻止了端口访问
