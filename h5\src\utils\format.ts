/**
 * 格式化工具函数
 * 提供常用的数据格式化方法
 */

import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'

// 配置dayjs
dayjs.locale('zh-cn')
dayjs.extend(relativeTime)
dayjs.extend(duration)

/**
 * 格式化金额
 * @param amount 金额
 * @param options 格式化选项
 * @returns 格式化后的金额字符串
 */
export const formatCurrency = (
  amount: number | string,
  options: {
    currency?: string
    minimumFractionDigits?: number
    maximumFractionDigits?: number
    showSymbol?: boolean
  } = {}
): string => {
  const {
    currency = 'CNY',
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    showSymbol = true
  } = options

  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  
  if (isNaN(num)) return '0.00'

  const formatted = new Intl.NumberFormat('zh-CN', {
    style: showSymbol ? 'currency' : 'decimal',
    currency,
    minimumFractionDigits,
    maximumFractionDigits
  }).format(num)

  return formatted
}

/**
 * 格式化数字（添加千分位分隔符）
 * @param num 数字
 * @param decimals 小数位数
 * @returns 格式化后的数字字符串
 */
export const formatNumber = (num: number | string, decimals?: number): string => {
  const number = typeof num === 'string' ? parseFloat(num) : num
  
  if (isNaN(number)) return '0'

  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number)
}

/**
 * 格式化百分比
 * @param value 数值（0-1之间）
 * @param decimals 小数位数
 * @returns 格式化后的百分比字符串
 */
export const formatPercent = (value: number, decimals: number = 2): string => {
  if (isNaN(value)) return '0%'
  
  return new Intl.NumberFormat('zh-CN', {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`
}

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (
  date: string | number | Date | dayjs.Dayjs,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string => {
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: string | number | Date | dayjs.Dayjs): string => {
  return dayjs(date).fromNow()
}

/**
 * 格式化时长
 * @param seconds 秒数
 * @returns 格式化后的时长字符串
 */
export const formatDuration = (seconds: number): string => {
  const duration = dayjs.duration(seconds, 'seconds')
  
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${duration.minutes()}分${duration.seconds()}秒`
  } else if (seconds < 86400) {
    return `${duration.hours()}小时${duration.minutes()}分`
  } else {
    return `${duration.days()}天${duration.hours()}小时`
  }
}

/**
 * 格式化手机号（隐藏中间4位）
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export const formatPhone = (phone: string): string => {
  if (!phone || phone.length !== 11) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 格式化身份证号（隐藏中间部分）
 * @param idCard 身份证号
 * @returns 格式化后的身份证号
 */
export const formatIdCard = (idCard: string): string => {
  if (!idCard || idCard.length !== 18) return idCard
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

/**
 * 格式化银行卡号（每4位一组）
 * @param cardNumber 银行卡号
 * @returns 格式化后的银行卡号
 */
export const formatBankCard = (cardNumber: string): string => {
  if (!cardNumber) return cardNumber
  return cardNumber.replace(/\s/g, '').replace(/(\d{4})(?=\d)/g, '$1 ')
}

/**
 * 格式化姓名（隐藏中间字符）
 * @param name 姓名
 * @returns 格式化后的姓名
 */
export const formatName = (name: string): string => {
  if (!name) return name
  
  if (name.length === 2) {
    return name.charAt(0) + '*'
  } else if (name.length > 2) {
    return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1)
  }
  
  return name
}

/**
 * 格式化地址（保留前后部分，隐藏中间）
 * @param address 地址
 * @param keepStart 保留开头字符数
 * @param keepEnd 保留结尾字符数
 * @returns 格式化后的地址
 */
export const formatAddress = (address: string, keepStart: number = 6, keepEnd: number = 6): string => {
  if (!address || address.length <= keepStart + keepEnd) return address
  
  const start = address.substring(0, keepStart)
  const end = address.substring(address.length - keepEnd)
  const middle = '*'.repeat(Math.min(6, address.length - keepStart - keepEnd))
  
  return start + middle + end
}

/**
 * 格式化数字为中文
 * @param num 数字
 * @returns 中文数字字符串
 */
export const formatChineseNumber = (num: number): string => {
  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿']
  
  if (num === 0) return '零'
  if (num < 0) return '负' + formatChineseNumber(-num)
  
  let result = ''
  let unitIndex = 0
  
  while (num > 0) {
    const digit = num % 10
    if (digit !== 0) {
      result = digits[digit] + units[unitIndex] + result
    } else if (result && !result.startsWith('零')) {
      result = '零' + result
    }
    num = Math.floor(num / 10)
    unitIndex++
  }
  
  return result
}

/**
 * 格式化URL参数
 * @param params 参数对象
 * @returns URL参数字符串
 */
export const formatUrlParams = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  return searchParams.toString()
}

/**
 * 格式化HTML为纯文本
 * @param html HTML字符串
 * @param maxLength 最大长度
 * @returns 纯文本字符串
 */
export const formatHtmlToText = (html: string, maxLength?: number): string => {
  const text = html.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ').trim()
  
  if (maxLength && text.length > maxLength) {
    return text.substring(0, maxLength) + '...'
  }
  
  return text
}

/**
 * 格式化文本省略
 * @param text 文本
 * @param maxLength 最大长度
 * @param suffix 省略后缀
 * @returns 格式化后的文本
 */
export const formatEllipsis = (text: string, maxLength: number, suffix: string = '...'): string => {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength - suffix.length) + suffix
}

export default {
  formatCurrency,
  formatNumber,
  formatPercent,
  formatFileSize,
  formatDate,
  formatRelativeTime,
  formatDuration,
  formatPhone,
  formatIdCard,
  formatBankCard,
  formatName,
  formatAddress,
  formatChineseNumber,
  formatUrlParams,
  formatHtmlToText,
  formatEllipsis
}
