/**
 * 设备检测工具函数
 * 提供设备类型、浏览器类型、操作系统等检测功能
 */

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isIOS: boolean
  isAndroid: boolean
  isWindows: boolean
  isMac: boolean
  isLinux: boolean
  isWechat: boolean
  isAlipay: boolean
  isQQ: boolean
  isWeibo: boolean
  browser: string
  browserVersion: string
  os: string
  osVersion: string
  screenWidth: number
  screenHeight: number
  devicePixelRatio: number
}

/**
 * 获取用户代理字符串
 */
const getUserAgent = (): string => {
  return navigator.userAgent.toLowerCase()
}

/**
 * 检测是否为移动设备
 */
export const isMobile = (): boolean => {
  const ua = getUserAgent()
  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua)
}

/**
 * 检测是否为平板设备
 */
export const isTablet = (): boolean => {
  const ua = getUserAgent()
  return /ipad|android(?!.*mobile)|tablet/i.test(ua)
}

/**
 * 检测是否为桌面设备
 */
export const isDesktop = (): boolean => {
  return !isMobile() && !isTablet()
}

/**
 * 检测是否为iOS设备
 */
export const isIOS = (): boolean => {
  const ua = getUserAgent()
  return /iphone|ipad|ipod/i.test(ua)
}

/**
 * 检测是否为Android设备
 */
export const isAndroid = (): boolean => {
  const ua = getUserAgent()
  return /android/i.test(ua)
}

/**
 * 检测是否为Windows系统
 */
export const isWindows = (): boolean => {
  const ua = getUserAgent()
  return /windows/i.test(ua)
}

/**
 * 检测是否为Mac系统
 */
export const isMac = (): boolean => {
  const ua = getUserAgent()
  return /macintosh|mac os x/i.test(ua)
}

/**
 * 检测是否为Linux系统
 */
export const isLinux = (): boolean => {
  const ua = getUserAgent()
  return /linux/i.test(ua) && !isAndroid()
}

/**
 * 检测是否在微信浏览器中
 */
export const isWechat = (): boolean => {
  const ua = getUserAgent()
  return /micromessenger/i.test(ua)
}

/**
 * 检测是否在支付宝浏览器中
 */
export const isAlipay = (): boolean => {
  const ua = getUserAgent()
  return /alipay/i.test(ua)
}

/**
 * 检测是否在QQ浏览器中
 */
export const isQQ = (): boolean => {
  const ua = getUserAgent()
  return /qq/i.test(ua)
}

/**
 * 检测是否在微博浏览器中
 */
export const isWeibo = (): boolean => {
  const ua = getUserAgent()
  return /weibo/i.test(ua)
}

/**
 * 获取浏览器类型
 */
export const getBrowser = (): { name: string; version: string } => {
  const ua = getUserAgent()
  
  let name = 'unknown'
  let version = 'unknown'
  
  if (ua.includes('chrome') && !ua.includes('edge')) {
    name = 'chrome'
    const match = ua.match(/chrome\/(\d+)/)
    version = match ? match[1] : 'unknown'
  } else if (ua.includes('firefox')) {
    name = 'firefox'
    const match = ua.match(/firefox\/(\d+)/)
    version = match ? match[1] : 'unknown'
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    name = 'safari'
    const match = ua.match(/version\/(\d+)/)
    version = match ? match[1] : 'unknown'
  } else if (ua.includes('edge')) {
    name = 'edge'
    const match = ua.match(/edge\/(\d+)/)
    version = match ? match[1] : 'unknown'
  } else if (ua.includes('trident') || ua.includes('msie')) {
    name = 'ie'
    const match = ua.match(/(?:msie |rv:)(\d+)/)
    version = match ? match[1] : 'unknown'
  }
  
  return { name, version }
}

/**
 * 获取操作系统信息
 */
export const getOS = (): { name: string; version: string } => {
  const ua = getUserAgent()
  
  let name = 'unknown'
  let version = 'unknown'
  
  if (isIOS()) {
    name = 'ios'
    const match = ua.match(/os (\d+)_(\d+)/)
    version = match ? `${match[1]}.${match[2]}` : 'unknown'
  } else if (isAndroid()) {
    name = 'android'
    const match = ua.match(/android (\d+)\.(\d+)/)
    version = match ? `${match[1]}.${match[2]}` : 'unknown'
  } else if (isWindows()) {
    name = 'windows'
    if (ua.includes('windows nt 10.0')) version = '10'
    else if (ua.includes('windows nt 6.3')) version = '8.1'
    else if (ua.includes('windows nt 6.2')) version = '8'
    else if (ua.includes('windows nt 6.1')) version = '7'
  } else if (isMac()) {
    name = 'macos'
    const match = ua.match(/mac os x (\d+)_(\d+)/)
    version = match ? `${match[1]}.${match[2]}` : 'unknown'
  } else if (isLinux()) {
    name = 'linux'
  }
  
  return { name, version }
}

/**
 * 获取屏幕信息
 */
export const getScreenInfo = (): {
  width: number
  height: number
  availWidth: number
  availHeight: number
  devicePixelRatio: number
} => {
  return {
    width: screen.width,
    height: screen.height,
    availWidth: screen.availWidth,
    availHeight: screen.availHeight,
    devicePixelRatio: window.devicePixelRatio || 1
  }
}

/**
 * 获取视口信息
 */
export const getViewportInfo = (): {
  width: number
  height: number
  scrollX: number
  scrollY: number
} => {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    scrollX: window.scrollX || window.pageXOffset,
    scrollY: window.scrollY || window.pageYOffset
  }
}

/**
 * 检测是否支持触摸
 */
export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 检测是否支持WebP格式
 */
export const supportsWebP = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image()
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2)
    }
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
  })
}

/**
 * 检测网络连接类型
 */
export const getNetworkType = (): string => {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection

  if (connection) {
    return connection.effectiveType || connection.type || 'unknown'
  }

  return 'unknown'
}

/**
 * 检测是否在线
 */
export const isOnline = (): boolean => {
  return navigator.onLine
}

/**
 * 获取完整的设备信息
 */
export const getDeviceInfo = (): DeviceInfo => {
  const browser = getBrowser()
  const os = getOS()
  const screen = getScreenInfo()
  
  return {
    isMobile: isMobile(),
    isTablet: isTablet(),
    isDesktop: isDesktop(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    isWindows: isWindows(),
    isMac: isMac(),
    isLinux: isLinux(),
    isWechat: isWechat(),
    isAlipay: isAlipay(),
    isQQ: isQQ(),
    isWeibo: isWeibo(),
    browser: browser.name,
    browserVersion: browser.version,
    os: os.name,
    osVersion: os.version,
    screenWidth: screen.width,
    screenHeight: screen.height,
    devicePixelRatio: screen.devicePixelRatio
  }
}

/**
 * 监听网络状态变化
 */
export const onNetworkChange = (callback: (isOnline: boolean) => void): (() => void) => {
  const handleOnline = () => callback(true)
  const handleOffline = () => callback(false)
  
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 返回取消监听的函数
  return () => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
  }
}

/**
 * 监听屏幕方向变化
 */
export const onOrientationChange = (callback: (orientation: string) => void): (() => void) => {
  const handleOrientationChange = () => {
    const orientation = screen.orientation?.type || 
                       (window.orientation === 90 || window.orientation === -90 ? 'landscape' : 'portrait')
    callback(orientation)
  }
  
  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleOrientationChange)
  
  // 返回取消监听的函数
  return () => {
    window.removeEventListener('orientationchange', handleOrientationChange)
    window.removeEventListener('resize', handleOrientationChange)
  }
}

export default {
  isMobile,
  isTablet,
  isDesktop,
  isIOS,
  isAndroid,
  isWindows,
  isMac,
  isLinux,
  isWechat,
  isAlipay,
  isQQ,
  isWeibo,
  getBrowser,
  getOS,
  getScreenInfo,
  getViewportInfo,
  isTouchDevice,
  supportsWebP,
  getNetworkType,
  isOnline,
  getDeviceInfo,
  onNetworkChange,
  onOrientationChange
}
